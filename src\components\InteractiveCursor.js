import React, { useState, useEffect } from 'react';

const InteractiveCursor = () => {
  const [trail, setTrail] = useState([]);

  useEffect(() => {
    let animationId;

    const updateMousePosition = (e) => {
      // Add to trail with throttling
      setTrail(prev => {
        const newPoint = {
          x: e.clientX,
          y: e.clientY,
          id: Date.now() + Math.random(),
          opacity: 1
        };
        const newTrail = [...prev, newPoint];
        return newTrail.slice(-6); // Keep only last 6 positions for performance
      });
    };

    // Throttle mouse events for better performance
    let lastTime = 0;
    const throttledMouseMove = (e) => {
      const now = Date.now();
      if (now - lastTime > 16) { // ~60fps
        updateMousePosition(e);
        lastTime = now;
      }
    };

    // Fade out trail points
    const fadeTrail = () => {
      setTrail(prev =>
        prev.map((point, index) => ({
          ...point,
          opacity: (index + 1) / prev.length * 0.8 // Fade based on position
        })).filter(point => point.opacity > 0.1)
      );
      animationId = requestAnimationFrame(fadeTrail);
    };

    window.addEventListener('mousemove', throttledMouseMove);
    fadeTrail();

    return () => {
      window.removeEventListener('mousemove', throttledMouseMove);
      if (animationId) {
        cancelAnimationFrame(animationId);
      }
    };
  }, []);

  return (
    <>
      {/* Lightweight Cursor Trail */}
      {trail.map((point, index) => (
        <div
          key={point.id}
          style={{
            position: 'fixed',
            left: point.x - 3,
            top: point.y - 3,
            width: '6px',
            height: '6px',
            backgroundColor: `rgba(0, 212, 255, ${point.opacity})`,
            borderRadius: '50%',
            pointerEvents: 'none',
            zIndex: 9998,
            transform: `scale(${point.opacity})`,
            boxShadow: `0 0 ${point.opacity * 10}px rgba(0, 212, 255, ${point.opacity * 0.5})`
          }}
        />
      ))}
    </>
  );
};

export default InteractiveCursor;
