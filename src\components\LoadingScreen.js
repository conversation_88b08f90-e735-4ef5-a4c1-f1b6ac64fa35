import { useState, useEffect } from 'react';
import { FaCode, FaRocket, FaStar } from 'react-icons/fa';

const LoadingScreen = ({ onLoadingComplete }) => {
  const [progress, setProgress] = useState(0);
  const [currentText, setCurrentText] = useState('');
  const [isVisible, setIsVisible] = useState(true);

  const loadingTexts = [
    'Initializing Portfolio...',
    'Loading Components...',
    'Preparing Experience...',
    'Setting up Interactions...',
    'Almost Ready...',
    'Welcome!'
  ];

  useEffect(() => {
    const interval = setInterval(() => {
      setProgress(prev => {
        const newProgress = prev + Math.random() * 15 + 5;
        
        // Update text based on progress
        const textIndex = Math.min(
          Math.floor((newProgress / 100) * loadingTexts.length),
          loadingTexts.length - 1
        );
        setCurrentText(loadingTexts[textIndex]);
        
        if (newProgress >= 100) {
          clearInterval(interval);
          
          // Start exit animation after a brief pause
          setTimeout(() => {
            setIsVisible(false);
            setTimeout(() => {
              onLoadingComplete();
            }, 800);
          }, 500);
          
          return 100;
        }
        
        return newProgress;
      });
    }, 100);

    return () => clearInterval(interval);
  }, [onLoadingComplete]);

  if (!isVisible) {
    return (
      <div
        style={{
          position: 'fixed',
          top: 0,
          left: 0,
          width: '100%',
          height: '100%',
          background: 'linear-gradient(135deg, #0a0a0a 0%, #1a1a1a 100%)',
          zIndex: 10000,
          animation: 'fadeOut 0.8s ease-out forwards'
        }}
      >
        <style>
          {`
            @keyframes fadeOut {
              0% { opacity: 1; transform: scale(1); }
              100% { opacity: 0; transform: scale(1.1); }
            }
          `}
        </style>
      </div>
    );
  }

  return (
    <div
      style={{
        position: 'fixed',
        top: 0,
        left: 0,
        width: '100%',
        height: '100%',
        background: 'linear-gradient(135deg, #0a0a0a 0%, #1a1a1a 100%)',
        display: 'flex',
        flexDirection: 'column',
        alignItems: 'center',
        justifyContent: 'center',
        zIndex: 10000,
        color: '#fff'
      }}
    >
      {/* Animated Background */}
      <div
        style={{
          position: 'absolute',
          top: 0,
          left: 0,
          width: '100%',
          height: '100%',
          background: `
            radial-gradient(circle at 20% 20%, rgba(0, 212, 255, 0.1) 0%, transparent 50%),
            radial-gradient(circle at 80% 80%, rgba(78, 205, 196, 0.1) 0%, transparent 50%),
            radial-gradient(circle at 50% 50%, rgba(255, 107, 107, 0.05) 0%, transparent 70%)
          `,
          animation: 'backgroundPulse 3s ease-in-out infinite'
        }}
      />

      {/* Logo Animation */}
      <div
        style={{
          marginBottom: '3rem',
          position: 'relative'
        }}
      >
        <div
          style={{
            display: 'flex',
            alignItems: 'center',
            gap: '1rem',
            fontSize: '3rem',
            marginBottom: '1rem'
          }}
        >
          <FaCode 
            style={{
              color: '#00d4ff',
              animation: 'bounce 2s ease-in-out infinite'
            }}
          />
          <FaRocket 
            style={{
              color: '#4ecdc4',
              animation: 'bounce 2s ease-in-out infinite 0.3s'
            }}
          />
          <FaStar 
            style={{
              color: '#ff6b6b',
              animation: 'bounce 2s ease-in-out infinite 0.6s'
            }}
          />
        </div>
        
        <h1
          style={{
            fontSize: '2.5rem',
            fontWeight: '700',
            background: 'linear-gradient(135deg, #00d4ff 0%, #4ecdc4 50%, #ff6b6b 100%)',
            backgroundClip: 'text',
            WebkitBackgroundClip: 'text',
            WebkitTextFillColor: 'transparent',
            textAlign: 'center',
            margin: 0,
            animation: 'glow 2s ease-in-out infinite alternate'
          }}
        >
          Portfolio
        </h1>
      </div>

      {/* Progress Bar */}
      <div
        style={{
          width: '300px',
          height: '6px',
          background: 'rgba(255, 255, 255, 0.1)',
          borderRadius: '3px',
          overflow: 'hidden',
          marginBottom: '2rem',
          position: 'relative'
        }}
      >
        <div
          style={{
            width: `${progress}%`,
            height: '100%',
            background: 'linear-gradient(90deg, #00d4ff 0%, #4ecdc4 50%, #ff6b6b 100%)',
            borderRadius: '3px',
            transition: 'width 0.3s ease',
            position: 'relative'
          }}
        >
          {/* Shimmer effect */}
          <div
            style={{
              position: 'absolute',
              top: 0,
              left: 0,
              width: '100%',
              height: '100%',
              background: 'linear-gradient(90deg, transparent, rgba(255,255,255,0.3), transparent)',
              animation: 'shimmer 1.5s infinite'
            }}
          />
        </div>
      </div>

      {/* Progress Text */}
      <div
        style={{
          textAlign: 'center',
          marginBottom: '1rem'
        }}
      >
        <div
          style={{
            fontSize: '1.2rem',
            fontWeight: '600',
            color: '#00d4ff',
            marginBottom: '0.5rem'
          }}
        >
          {currentText}
        </div>
        <div
          style={{
            fontSize: '2rem',
            fontWeight: '700',
            color: '#fff'
          }}
        >
          {Math.round(progress)}%
        </div>
      </div>

      {/* Floating Particles */}
      {[...Array(6)].map((_, i) => (
        <div
          key={i}
          style={{
            position: 'absolute',
            width: '4px',
            height: '4px',
            background: ['#00d4ff', '#4ecdc4', '#ff6b6b'][i % 3],
            borderRadius: '50%',
            top: `${20 + Math.random() * 60}%`,
            left: `${10 + Math.random() * 80}%`,
            animation: `float ${3 + Math.random() * 2}s ease-in-out infinite`,
            animationDelay: `${Math.random() * 2}s`,
            opacity: 0.7
          }}
        />
      ))}

      <style>
        {`
          @keyframes bounce {
            0%, 20%, 53%, 80%, 100% { transform: translateY(0); }
            40%, 43% { transform: translateY(-15px); }
            70% { transform: translateY(-8px); }
            90% { transform: translateY(-3px); }
          }
          
          @keyframes glow {
            0% { filter: brightness(1) drop-shadow(0 0 5px rgba(0, 212, 255, 0.5)); }
            100% { filter: brightness(1.2) drop-shadow(0 0 20px rgba(0, 212, 255, 0.8)); }
          }
          
          @keyframes shimmer {
            0% { transform: translateX(-100%); }
            100% { transform: translateX(100%); }
          }
          
          @keyframes backgroundPulse {
            0%, 100% { opacity: 1; }
            50% { opacity: 0.8; }
          }
          
          @keyframes float {
            0%, 100% { transform: translateY(0px) rotate(0deg); }
            50% { transform: translateY(-20px) rotate(180deg); }
          }
        `}
      </style>
    </div>
  );
};

export default LoadingScreen;
