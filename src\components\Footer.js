import React from 'react';
import { <PERSON>a<PERSON><PERSON><PERSON>, <PERSON>a<PERSON><PERSON><PERSON><PERSON>, <PERSON>a<PERSON><PERSON><PERSON>ram, Fa<PERSON><PERSON><PERSON>, FaHeart, FaArrowUp } from 'react-icons/fa';
import { personalInfo } from '../data/portfolioData';
import './Footer.css';

const Footer = () => {
  const scrollToTop = () => {
    window.scrollTo({
      top: 0,
      behavior: 'smooth'
    });
  };

  const socialIcons = [
    { icon: FaGithub, link: personalInfo.social.github, name: 'GitHub' },
    { icon: FaLinkedin, link: personalInfo.social.linkedin, name: 'LinkedIn' },
    { icon: FaInstagram, link: personalInfo.social.instagram, name: 'Instagram' },
    { icon: FaTwitter, link: personalInfo.social.twitter, name: 'Twitter' }
  ];

  const quickLinks = [
    { name: 'Home', href: '#home' },
    { name: 'About', href: '#about' },
    { name: 'Skills', href: '#skills' },
    { name: 'Projects', href: '#projects' },
    { name: 'Experience', href: '#experience' },
    { name: 'Contact', href: '#contact' }
  ];

  return (
    <footer className="footer">
      <div className="container">
        <div className="footer-content">
          <div className="footer-section">
            <div className="footer-logo">
              <h3>{personalInfo.name}</h3>
              <p>{personalInfo.title}</p>
            </div>
            <p className="footer-description">
              Creating amazing digital experiences with modern technologies. 
              Let's build something great together!
            </p>
            <div className="footer-social">
              {socialIcons.map((social, index) => (
                <a
                  key={index}
                  href={social.link}
                  target="_blank"
                  rel="noopener noreferrer"
                  className="social-link"
                  title={social.name}
                >
                  <social.icon />
                </a>
              ))}
            </div>
          </div>
          
          <div className="footer-section">
            <h4>Quick Links</h4>
            <div className="footer-links">
              {quickLinks.map((link, index) => (
                <a key={index} href={link.href} className="footer-link">
                  {link.name}
                </a>
              ))}
            </div>
          </div>
          
          <div className="footer-section">
            <h4>Contact Info</h4>
            <div className="contact-info">
              <p>{personalInfo.email}</p>
              <p>{personalInfo.phone}</p>
              <p>{personalInfo.location}</p>
            </div>
          </div>
        </div>
        
        <div className="footer-bottom">
          <div className="footer-copyright">
            <p>
              © {new Date().getFullYear()} {personalInfo.name}. Made with{' '}
              <FaHeart className="heart-icon" /> using React
            </p>
          </div>
          
          <button className="scroll-to-top" onClick={scrollToTop} title="Back to top">
            <FaArrowUp />
          </button>
        </div>
      </div>
    </footer>
  );
};

export default Footer;
