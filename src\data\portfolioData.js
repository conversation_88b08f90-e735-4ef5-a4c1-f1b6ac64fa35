// ========================================
// EDIT BAGIAN INI UNTUK MENYESUAIKAN PORTFOLIO ANDA
// ========================================

export const personalInfo = {
  name: "Alfarizi Aprilliano",
  title: "Front End Developer",
  subtitle: "Passionate about creating beautiful user interfaces",
  email: "<EMAIL>",
  phone: "+62 831 2375 7272",
  location: "Purworejo, Indonesia",
  
  // Social Media Links
  social: {
    github: "https://github.com/alfarizi-front",
    linkedin: "https://linkedin.com/in/username",
    instagram: "https://instagram.com/username",
    twitter: "https://twitter.com/username"
  },
  
  // About Me
  about: `saya ngoding cuman pas mood doang,set<PERSON><PERSON>ya saya scroll fesnuk`,
  
  // Resume/CV Link
  resumeLink: "https://drive.google.com/file/d/your-resume-link"
};

export const skills = [
  {
    category: "Frontend",
    items: ["React", "JavaScript", "TypeScript", "HTML5", "CSS3", "Tailwind CSS", "Next.js"]
  },
  {
    category: "Backend",
    items: ["Node.js", "Express.js", "Python", "PHP", "MySQL", "MongoDB", "PostgreSQL"]
  },
  {
    category: "Tools & Others",
    items: ["Git", "Docker", "AWS", "Figma", "Photoshop", "Linux", "REST API"]
  }
];

export const projects = [
  {
    id: 1,
    title: "E-Commerce Website",
    description: "Full-stack e-commerce platform dengan fitur lengkap seperti shopping cart, payment gateway, dan admin dashboard.",
    image: "/images/project1.jpg", // Ganti dengan path gambar Anda
    technologies: ["React", "Node.js", "MongoDB", "Stripe"],
    liveLink: "https://your-project-link.com",
    githubLink: "https://github.com/username/project",
    featured: true
  },
  {
    id: 2,
    title: "Task Management App",
    description: "Aplikasi manajemen tugas dengan fitur real-time collaboration dan drag & drop interface.",
    image: "/images/project2.jpg",
    technologies: ["React", "Firebase", "Material-UI"],
    liveLink: "https://your-project-link.com",
    githubLink: "https://github.com/username/project",
    featured: true
  },
  {
    id: 3,
    title: "Weather Dashboard",
    description: "Dashboard cuaca interaktif dengan visualisasi data dan prediksi cuaca 7 hari ke depan.",
    image: "/images/project3.jpg",
    technologies: ["JavaScript", "Chart.js", "Weather API"],
    liveLink: "https://your-project-link.com",
    githubLink: "https://github.com/username/project",
    featured: false
  }
];

export const experience = [
  {
    id: 1,
    company: "Tech Company",
    position: "Senior Frontend Developer",
    duration: "2022 - Present",
    description: "Mengembangkan dan memelihara aplikasi web menggunakan React dan TypeScript. Memimpin tim frontend dan berkolaborasi dengan tim backend untuk mengoptimalkan performa aplikasi."
  },
  {
    id: 2,
    company: "Digital Agency",
    position: "Full Stack Developer",
    duration: "2020 - 2022",
    description: "Membangun website dan aplikasi web untuk berbagai klien menggunakan berbagai teknologi seperti React, Node.js, dan PHP."
  },
  {
    id: 3,
    company: "Startup Company",
    position: "Junior Developer",
    duration: "2019 - 2020",
    description: "Memulai karir sebagai developer dengan fokus pada pengembangan frontend menggunakan HTML, CSS, JavaScript, dan React."
  }
];

export const education = [
  {
    id: 1,
    institution: "Universitas Indonesia",
    degree: "S1 Teknik Informatika",
    duration: "2015 - 2019",
    description: "Fokus pada pengembangan software dan algoritma. IPK: 3.75"
  }
];

// Warna tema - Anda bisa mengubah warna sesuai preferensi
export const theme = {
  primary: "#00d4ff",
  secondary: "#ff6b6b",
  accent: "#4ecdc4",
  background: "#0a0a0a",
  surface: "#1a1a1a",
  text: "#ffffff"
};
