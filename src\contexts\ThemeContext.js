import React, { createContext, useContext, useState, useEffect } from 'react';

const ThemeContext = createContext();

export const useTheme = () => {
  const context = useContext(ThemeContext);
  if (!context) {
    throw new Error('useTheme must be used within a ThemeProvider');
  }
  return context;
};

export const ThemeProvider = ({ children }) => {
  // Always dark mode - no toggle needed
  const isDarkMode = true;
  const isTransitioning = false;

  // Apply dark theme to document
  useEffect(() => {
    document.documentElement.setAttribute('data-theme', 'dark');
  }, []);

  const theme = {
    isDarkMode,
    isTransitioning,
    colors: {
      primary: '#00d4ff',
      secondary: '#4ecdc4',
      accent: '#ff6b6b',
      background: '#0a0a0a',
      surface: '#1a1a1a',
      text: '#ffffff',
      textSecondary: '#cccccc',
      textMuted: '#999999',
      border: 'rgba(255, 255, 255, 0.1)',
      borderHover: 'rgba(0, 212, 255, 0.3)'
    }
  };

  return (
    <ThemeContext.Provider value={theme}>
      {children}
    </ThemeContext.Provider>
  );
};
