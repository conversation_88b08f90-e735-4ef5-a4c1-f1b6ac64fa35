# Portfolio Website

Website portfolio modern yang dibuat dengan React, HTML, dan CSS dengan desain yang interaktif dan responsif.

## 🚀 Fitur

- **Desain Modern**: UI/UX yang clean dan profesional
- **Responsif**: Tampil sempurna di semua perangkat
- **Interaktif**: Animasi dan efek hover yang menarik
- **Typing Effect**: Efek mengetik pada hero section
- **Smooth Scrolling**: Navigasi yang halus antar section
- **Contact Form**: Form kontak yang fungsional
- **Social Media Links**: Integrasi dengan media sosial

## 📁 Struktur Project

```
portfolio/
├── public/
│   └── index.html
├── src/
│   ├── components/
│   │   ├── Navbar.js & Navbar.css
│   │   ├── Hero.js & Hero.css
│   │   ├── About.js & About.css
│   │   ├── Skills.js & Skills.css
│   │   ├── Projects.js & Projects.css
│   │   ├── Experience.js & Experience.css
│   │   ├── Contact.js & Contact.css
│   │   └── Footer.js & Footer.css
│   ├── data/
│   │   └── portfolioData.js (FILE UTAMA UNTUK EDIT)
│   ├── App.js
│   ├── App.css
│   ├── index.js
│   └── index.css
├── package.json
└── README.md
```

## 🎯 CARA MENGEDIT PORTFOLIO ANDA

### 1. Edit Informasi Personal
Buka file `src/data/portfolioData.js` dan edit bagian berikut:

```javascript
export const personalInfo = {
  name: "GANTI DENGAN NAMA ANDA",
  title: "GANTI DENGAN PROFESI ANDA",
  subtitle: "GANTI DENGAN DESKRIPSI SINGKAT",
  email: "GANTI DENGAN EMAIL ANDA",
  phone: "GANTI DENGAN NOMOR TELEPON",
  location: "GANTI DENGAN LOKASI ANDA",
  
  social: {
    github: "LINK GITHUB ANDA",
    linkedin: "LINK LINKEDIN ANDA", 
    instagram: "LINK INSTAGRAM ANDA",
    twitter: "LINK TWITTER ANDA"
  },
  
  about: `GANTI DENGAN DESKRIPSI LENGKAP TENTANG ANDA`,
  resumeLink: "LINK KE CV/RESUME ANDA"
};
```

### 2. Edit Skills
Masih di file yang sama, edit bagian skills:

```javascript
export const skills = [
  {
    category: "Frontend",
    items: ["GANTI DENGAN SKILL FRONTEND ANDA"]
  },
  {
    category: "Backend", 
    items: ["GANTI DENGAN SKILL BACKEND ANDA"]
  }
  // Tambah kategori lain sesuai kebutuhan
];
```

### 3. Edit Projects
Edit bagian projects dengan project Anda:

```javascript
export const projects = [
  {
    id: 1,
    title: "NAMA PROJECT",
    description: "DESKRIPSI PROJECT",
    image: "/images/project1.jpg", // Letakkan gambar di folder public/images/
    technologies: ["React", "Node.js"], // Teknologi yang digunakan
    liveLink: "LINK DEMO PROJECT",
    githubLink: "LINK GITHUB PROJECT",
    featured: true // true jika ingin ditampilkan sebagai featured
  }
  // Tambah project lain
];
```

### 4. Edit Experience & Education
Edit pengalaman kerja dan pendidikan:

```javascript
export const experience = [
  {
    id: 1,
    company: "NAMA PERUSAHAAN",
    position: "POSISI/JABATAN",
    duration: "PERIODE KERJA",
    description: "DESKRIPSI PEKERJAAN"
  }
];

export const education = [
  {
    id: 1,
    institution: "NAMA INSTITUSI",
    degree: "GELAR/JURUSAN",
    duration: "PERIODE",
    description: "DESKRIPSI"
  }
];
```

### 5. Ganti Warna Tema (Opsional)
Edit warna tema di bagian bawah file:

```javascript
export const theme = {
  primary: "#00d4ff",    // Warna utama
  secondary: "#ff6b6b",  // Warna sekunder
  accent: "#4ecdc4",     // Warna aksen
  // dst...
};
```

## 📸 Menambahkan Gambar

1. Buat folder `images` di dalam folder `public/`
2. Letakkan foto Anda dan screenshot project di folder tersebut
3. Update path gambar di `portfolioData.js`

Contoh struktur:
```
public/
├── images/
│   ├── profile.jpg
│   ├── project1.jpg
│   ├── project2.jpg
│   └── project3.jpg
└── index.html
```

## 🛠️ Instalasi & Menjalankan

### Prasyarat
- Node.js (versi 14 atau lebih baru)
- npm atau yarn

### Langkah Instalasi

1. **Install Dependencies**
   ```bash
   npm install
   ```

   Jika ada masalah dengan PowerShell, gunakan:
   ```bash
   powershell -ExecutionPolicy Bypass -Command "npm install"
   ```

2. **Jalankan Development Server**

   **Opsi 1: Menggunakan npm**
   ```bash
   npm start
   ```

   **Opsi 2: Menggunakan file batch (Windows)**
   Double-click file `start-dev.bat`

   **Opsi 3: Manual**
   ```bash
   npx react-scripts start
   ```

   Website akan terbuka di `http://localhost:3000`

3. **Build untuk Production**
   ```bash
   npm run build
   ```

### Troubleshooting
- Jika ada error PowerShell, jalankan sebagai Administrator
- Pastikan Node.js sudah terinstall dengan `node --version`
- Jika port 3000 sudah digunakan, React akan otomatis menggunakan port lain

## 📱 Responsive Design

Website ini sudah dioptimasi untuk:
- Desktop (1200px+)
- Tablet (768px - 1199px)
- Mobile (< 768px)

## 🎨 Customization Lanjutan

Jika ingin mengubah styling lebih lanjut:

1. **Warna Global**: Edit `src/App.css`
2. **Komponen Spesifik**: Edit file CSS masing-masing komponen
3. **Font**: Ganti di `public/index.html` dan `src/index.css`

## 📞 Support

Jika ada pertanyaan atau butuh bantuan, silakan hubungi saya!

---

**Selamat menggunakan portfolio website Anda! 🎉**
