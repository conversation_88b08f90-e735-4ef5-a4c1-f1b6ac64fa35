.skills {
  background: linear-gradient(135deg, #0a0a0a 0%, #1a1a1a 100%);
  position: relative;
}

.skills::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-image: 
    radial-gradient(circle at 20% 20%, rgba(78, 205, 196, 0.05) 0%, transparent 50%),
    radial-gradient(circle at 80% 80%, rgba(0, 212, 255, 0.05) 0%, transparent 50%);
  pointer-events: none;
}

/* Interactive Tabs */
.skills-tabs {
  display: flex;
  justify-content: center;
  gap: 1rem;
  margin-bottom: 3rem;
  position: relative;
  z-index: 1;
}

.skill-tab {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  background: rgba(255, 255, 255, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.2);
  color: #cccccc;
  padding: 1rem 2rem;
  border-radius: 50px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
  backdrop-filter: blur(10px);
}

.skill-tab:hover {
  background: rgba(0, 212, 255, 0.1);
  border-color: rgba(0, 212, 255, 0.3);
  color: #00d4ff;
  transform: translateY(-2px);
}

.skill-tab.active {
  background: linear-gradient(135deg, #00d4ff 0%, #4ecdc4 100%);
  border-color: transparent;
  color: #0a0a0a;
  transform: translateY(-2px);
  box-shadow: 0 10px 30px rgba(0, 212, 255, 0.3);
}

.tab-icon {
  font-size: 1.2rem;
}

/* Interactive Skills Display */
.skills-interactive {
  position: relative;
  z-index: 1;
}

.active-category {
  background: rgba(255, 255, 255, 0.05);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 30px;
  padding: 3rem;
  transition: all 0.3s ease;
}

.category-header-interactive {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 1.5rem;
  margin-bottom: 3rem;
  text-align: center;
}

.category-icon-large {
  font-size: 4rem;
  color: #00d4ff;
  animation: pulse 2s ease-in-out infinite;
}

@keyframes pulse {
  0%, 100% { transform: scale(1); }
  50% { transform: scale(1.1); }
}

.category-header-interactive h3 {
  font-size: 2.5rem;
  font-weight: 600;
  color: #ffffff;
  margin: 0;
}

.skills-grid-interactive {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 2rem;
}

.skills-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
  gap: 2rem;
  margin-bottom: 4rem;
  position: relative;
  z-index: 1;
}

.skill-category {
  background: rgba(255, 255, 255, 0.05);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 20px;
  padding: 2rem;
  transition: all 0.3s ease;
  animation-delay: 0.2s;
}

.skill-category:hover {
  transform: translateY(-10px);
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
  border-color: rgba(0, 212, 255, 0.3);
}

.category-header {
  display: flex;
  align-items: center;
  gap: 1rem;
  margin-bottom: 2rem;
  padding-bottom: 1rem;
  border-bottom: 2px solid rgba(0, 212, 255, 0.2);
}

.category-icon {
  font-size: 2rem;
  color: #00d4ff;
}

.category-title {
  font-size: 1.5rem;
  font-weight: 600;
  color: #ffffff;
}

.skills-list {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
}

.skill-item {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.skill-name {
  font-size: 1rem;
  font-weight: 500;
  color: #ffffff;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.skill-bar {
  width: 100%;
  height: 8px;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 4px;
  overflow: hidden;
  position: relative;
}

.skill-progress {
  height: 100%;
  background: linear-gradient(135deg, #00d4ff 0%, #4ecdc4 100%);
  border-radius: 4px;
  width: 0;
  animation: fillProgress 2s ease-out forwards;
  position: relative;
}

.skill-progress::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
  animation: shimmer 2s ease-in-out infinite;
}

@keyframes fillProgress {
  from {
    width: 0;
  }
  to {
    width: var(--progress-width);
  }
}

@keyframes shimmer {
  0% { transform: translateX(-100%); }
  100% { transform: translateX(100%); }
}

.skills-summary {
  display: flex;
  justify-content: center;
  position: relative;
  z-index: 1;
}

.summary-card {
  max-width: 600px;
  background: rgba(255, 255, 255, 0.05);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 20px;
  padding: 2.5rem;
  text-align: center;
  animation-delay: 0.6s;
}

.summary-card h4 {
  font-size: 1.5rem;
  font-weight: 600;
  color: #00d4ff;
  margin-bottom: 1rem;
}

.summary-card p {
  font-size: 1.1rem;
  line-height: 1.7;
  color: #cccccc;
}



/* Skill Tags Alternative Layout */
.skills-tags {
  display: flex;
  flex-wrap: wrap;
  gap: 1rem;
  margin-top: 1rem;
}

.skill-tag {
  background: linear-gradient(135deg, rgba(0, 212, 255, 0.1) 0%, rgba(78, 205, 196, 0.1) 100%);
  border: 1px solid rgba(0, 212, 255, 0.3);
  border-radius: 25px;
  padding: 0.5rem 1rem;
  font-size: 0.9rem;
  color: #ffffff;
  transition: all 0.3s ease;
}

.skill-tag:hover {
  background: linear-gradient(135deg, #00d4ff 0%, #4ecdc4 100%);
  color: #0a0a0a;
  transform: translateY(-2px);
}

/* Interactive Skill Cards */
.skill-card {
  background: rgba(255, 255, 255, 0.05);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 20px;
  padding: 2rem;
  text-align: center;
  transition: all 0.3s ease;
  cursor: pointer;
  position: relative;
  overflow: hidden;
}

.skill-card::after {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  width: 0;
  height: 0;
  background: radial-gradient(circle, rgba(0, 212, 255, 0.1) 0%, transparent 70%);
  border-radius: 50%;
  transform: translate(-50%, -50%);
  transition: all 0.6s ease;
  pointer-events: none;
}

.skill-card.hovered::after {
  width: 300px;
  height: 300px;
  animation: ripple 1.5s ease-out;
}

.skill-card.animated {
  border-color: rgba(0, 212, 255, 0.3);
  background: rgba(0, 212, 255, 0.02);
}

.skill-card.animated .skill-name {
  color: #00d4ff;
}

.skill-card.animated .skill-icon {
  color: #4ecdc4;
}

@keyframes ripple {
  0% {
    width: 0;
    height: 0;
    opacity: 1;
  }
  100% {
    width: 300px;
    height: 300px;
    opacity: 0;
  }
}

/* Completed skill indicator */
.skill-card.animated::before {
  content: '✓';
  position: absolute;
  top: 1rem;
  right: 1rem;
  width: 24px;
  height: 24px;
  background: linear-gradient(135deg, #00d4ff 0%, #4ecdc4 100%);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 0.8rem;
  font-weight: bold;
  color: #ffffff;
  opacity: 0.7;
  transition: opacity 0.3s ease;
}

.skill-card.animated:hover::before {
  opacity: 1;
}

.skill-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(0, 212, 255, 0.1), transparent);
  transition: left 0.5s ease;
}

.skill-card:hover::before,
.skill-card.hovered::before {
  left: 100%;
}

.skill-card:hover,
.skill-card.hovered {
  transform: translateY(-10px) scale(1.05);
  border-color: rgba(0, 212, 255, 0.5);
  box-shadow: 0 20px 40px rgba(0, 212, 255, 0.2);
}

.skill-icon-wrapper {
  margin-bottom: 1.5rem;
}

.skill-icon {
  font-size: 3rem;
  color: #00d4ff;
  transition: all 0.3s ease;
}

.skill-card:hover .skill-icon,
.skill-card.hovered .skill-icon {
  color: #ffffff;
  transform: rotateY(360deg);
}

.skill-card h4 {
  font-size: 1.2rem;
  font-weight: 600;
  color: #ffffff;
  margin-bottom: 1rem;
}

.skill-level {
  margin-top: 1rem;
}

.skill-percentage {
  font-size: 1.8rem;
  font-weight: 700;
  color: #00d4ff;
  margin-bottom: 0.5rem;
  display: flex;
  align-items: baseline;
  justify-content: center;
  gap: 0.2rem;
  min-height: 2.5rem;
  transition: all 0.3s ease;
}

.percentage-number {
  font-size: 2.2rem;
  font-weight: 800;
  background: linear-gradient(135deg, #00d4ff 0%, #4ecdc4 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  transition: all 0.1s ease;
  text-shadow: 0 0 20px rgba(0, 212, 255, 0.3);
}

.percentage-symbol {
  font-size: 1.4rem;
  color: #cccccc;
  font-weight: 600;
}

.skill-card:hover .skill-percentage {
  transform: scale(1.1);
}

.skill-card:hover .percentage-number {
  animation: numberPulse 0.6s ease-in-out;
}

@keyframes numberPulse {
  0%, 100% {
    transform: scale(1);
    filter: brightness(1);
  }
  50% {
    transform: scale(1.15);
    filter: brightness(1.3);
    text-shadow: 0 0 30px rgba(0, 212, 255, 0.6);
  }
}

.skill-bar-interactive {
  width: 100%;
  height: 8px;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 4px;
  overflow: hidden;
  position: relative;
}

.skill-progress-interactive {
  height: 100%;
  background: linear-gradient(135deg, #00d4ff 0%, #4ecdc4 100%);
  border-radius: 4px;
  width: 0;
  transition: width 1.5s cubic-bezier(0.25, 0.46, 0.45, 0.94);
  position: relative;
  box-shadow: 0 0 10px rgba(0, 212, 255, 0.3);
}

.skill-card.hovered .skill-progress-interactive,
.skill-card.animated .skill-progress-interactive {
  box-shadow: 0 0 20px rgba(0, 212, 255, 0.6);
}

.skill-card.hovered .skill-progress-interactive {
  animation: progressGlow 1.5s ease-out;
}

.skill-progress-interactive.completed {
  box-shadow: 0 0 15px rgba(0, 212, 255, 0.5);
}

@keyframes progressGlow {
  0% {
    box-shadow: 0 0 5px rgba(0, 212, 255, 0.3);
  }
  50% {
    box-shadow: 0 0 25px rgba(0, 212, 255, 0.8);
  }
  100% {
    box-shadow: 0 0 20px rgba(0, 212, 255, 0.6);
  }
}

.skill-progress-interactive::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.4), transparent);
  animation: shimmer 2s ease-in-out infinite;
}

.skill-card.hovered .skill-progress-interactive::after {
  animation: shimmer 1s ease-in-out infinite;
}

.skill-progress-interactive.completed::after {
  animation: shimmer 3s ease-in-out infinite;
}

/* Mobile Responsive */
@media (max-width: 768px) {
  .skills-tabs {
    flex-wrap: wrap;
    gap: 0.5rem;
  }

  .skill-tab {
    padding: 0.8rem 1.5rem;
    font-size: 0.9rem;
  }

  .active-category {
    padding: 2rem;
  }

  .category-header-interactive {
    flex-direction: column;
    gap: 1rem;
  }

  .category-icon-large {
    font-size: 3rem;
  }

  .category-header-interactive h3 {
    font-size: 2rem;
  }

  .skills-grid-interactive {
    grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
    gap: 1rem;
  }

  .skill-card {
    padding: 1.5rem;
  }

  .skill-icon {
    font-size: 2.5rem;
  }

  .skills-grid {
    grid-template-columns: 1fr;
    gap: 1.5rem;
  }

  .skill-category {
    padding: 1.5rem;
  }

  .category-header {
    flex-direction: column;
    text-align: center;
    gap: 0.5rem;
  }

  .category-title {
    font-size: 1.3rem;
  }

  .summary-card {
    padding: 2rem;
    margin: 0 1rem;
  }

  .summary-card h4 {
    font-size: 1.3rem;
  }

  .summary-card p {
    font-size: 1rem;
  }
}
