.about {
  background: linear-gradient(135deg, #1a1a1a 0%, #0a0a0a 100%);
  position: relative;
}

.about::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-image: 
    radial-gradient(circle at 70% 30%, rgba(0, 212, 255, 0.05) 0%, transparent 50%),
    radial-gradient(circle at 30% 70%, rgba(255, 107, 107, 0.05) 0%, transparent 50%);
  pointer-events: none;
}

.about-content {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 4rem;
  align-items: center;
  position: relative;
  z-index: 1;
}

.about-text {
  animation-delay: 0.2s;
}

.about-intro {
  display: flex;
  align-items: center;
  gap: 1rem;
  margin-bottom: 2rem;
}

.about-icon {
  font-size: 2rem;
  color: #00d4ff;
}

.about-intro h3 {
  font-size: 2rem;
  font-weight: 600;
  color: #ffffff;
}

.about-description {
  font-size: 1.1rem;
  line-height: 1.8;
  color: #cccccc;
  margin-bottom: 2.5rem;
  text-align: justify;
}

.about-details {
  margin-bottom: 2.5rem;
}

.contact-item {
  display: flex;
  align-items: center;
  gap: 1rem;
  margin-bottom: 1rem;
  padding: 1rem;
  background: rgba(255, 255, 255, 0.05);
  border-radius: 10px;
  border: 1px solid rgba(255, 255, 255, 0.1);
  transition: all 0.3s ease;
}

.contact-item:hover {
  background: rgba(0, 212, 255, 0.1);
  border-color: rgba(0, 212, 255, 0.3);
  transform: translateX(5px);
}

.contact-icon {
  font-size: 1.2rem;
  color: #00d4ff;
  min-width: 20px;
}

.contact-info {
  display: flex;
  flex-direction: column;
}

.contact-label {
  font-size: 0.9rem;
  color: #999999;
  font-weight: 500;
}

.contact-value {
  font-size: 1rem;
  color: #ffffff;
  font-weight: 600;
}

.about-buttons {
  display: flex;
  gap: 1.5rem;
}

.about-image {
  display: flex;
  justify-content: center;
  animation-delay: 0.4s;
}

/* 3D Photo Container */
.image-wrapper-3d {
  position: relative;
  width: 350px;
  height: 450px;
  transform-style: preserve-3d;
  cursor: pointer;
  margin: 2rem auto;
}

.image-wrapper-3d.hovered {
  transform-style: preserve-3d;
}

.photo-3d-container {
  position: relative;
  width: 100%;
  height: 100%;
  transform-style: preserve-3d;
}

/* 3D Faces - Hanya Front Face */
.photo-face {
  position: absolute;
  width: 100%;
  height: 100%;
  border-radius: 20px;
  overflow: hidden;
}

.photo-face.front {
  transform: translateZ(0px);
}

/* Main Photo - Tanpa Border */
.about-photo-img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  border-radius: 20px;
  border: none;
  transition: all 0.3s ease;
  position: relative;
  z-index: 2;
}

.image-wrapper-3d:hover .about-photo-img {
  box-shadow:
    0 0 30px rgba(0, 212, 255, 0.4),
    inset 0 0 30px rgba(0, 212, 255, 0.1);
}

/* Photo Overlay */
.photo-overlay {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(
    135deg,
    rgba(0, 212, 255, 0.1) 0%,
    transparent 30%,
    transparent 70%,
    rgba(78, 205, 196, 0.1) 100%
  );
  border-radius: 20px;
  opacity: 0;
  transition: opacity 0.3s ease;
  pointer-events: none;
}

.image-wrapper-3d:hover .photo-overlay {
  opacity: 1;
}

.about-photo {
  width: 100%;
  height: 100%;
  background: linear-gradient(135deg, rgba(0, 212, 255, 0.1) 0%, rgba(255, 107, 107, 0.1) 100%);
  border-radius: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1.1rem;
  color: #cccccc;
  border: 2px solid rgba(0, 212, 255, 0.3);
  position: relative;
  overflow: hidden;
  backdrop-filter: blur(10px);
}

.about-photo::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.1), transparent);
  animation: shimmer 3s ease-in-out infinite;
}

@keyframes shimmer {
  0% { left: -100%; }
  100% { left: 100%; }
}

/* Hapus 3D Side Faces - Tidak diperlukan lagi */

/* Floating Elements */
.floating-elements {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  pointer-events: none;
  z-index: 3;
}

.floating-circle {
  position: absolute;
  border-radius: 50%;
  background: rgba(0, 212, 255, 0.6);
  animation: float-around 6s ease-in-out infinite;
}

.circle-1 {
  width: 20px;
  height: 20px;
  top: 10%;
  right: 10%;
  background: rgba(0, 212, 255, 0.7);
  animation-delay: 0s;
}

.circle-2 {
  width: 15px;
  height: 15px;
  bottom: 20%;
  left: 15%;
  background: rgba(78, 205, 196, 0.7);
  animation-delay: 2s;
}

.circle-3 {
  width: 12px;
  height: 12px;
  top: 60%;
  right: 20%;
  background: rgba(255, 107, 107, 0.7);
  animation-delay: 4s;
}

@keyframes float-around {
  0%, 100% {
    transform: translateY(0px) translateX(0px) scale(1);
    opacity: 0.6;
  }
  25% {
    transform: translateY(-15px) translateX(10px) scale(1.1);
    opacity: 0.8;
  }
  50% {
    transform: translateY(-10px) translateX(-5px) scale(0.9);
    opacity: 1;
  }
  75% {
    transform: translateY(5px) translateX(-10px) scale(1.05);
    opacity: 0.7;
  }
}

/* Light Effect */
.light-effect {
  position: absolute;
  top: -50%;
  left: -50%;
  width: 200%;
  height: 200%;
  background: radial-gradient(
    circle at center,
    rgba(0, 212, 255, 0.1) 0%,
    transparent 70%
  );
  opacity: 0;
  transition: opacity 0.5s ease;
  pointer-events: none;
  z-index: 1;
  animation: light-pulse 3s ease-in-out infinite;
}

.image-wrapper-3d:hover .light-effect {
  opacity: 1;
}

@keyframes light-pulse {
  0%, 100% {
    transform: scale(1) rotate(0deg);
    opacity: 0.3;
  }
  50% {
    transform: scale(1.1) rotate(180deg);
    opacity: 0.6;
  }
}

/* Mobile Responsive */
@media (max-width: 768px) {
  .about-content {
    grid-template-columns: 1fr;
    gap: 2rem;
    text-align: center;
  }
  
  .about-intro {
    justify-content: center;
  }
  
  .about-intro h3 {
    font-size: 1.5rem;
  }
  
  .about-description {
    font-size: 1rem;
    text-align: left;
  }
  
  .about-buttons {
    justify-content: center;
    flex-wrap: wrap;
  }
  
  .image-wrapper-3d {
    width: 280px;
    height: 350px;
    margin: 1rem auto;
  }

  /* Reduce 3D effect on mobile for performance */
  .image-wrapper-3d {
    transform: perspective(800px) !important;
  }

  .floating-circle {
    display: none; /* Hide floating elements on mobile */
  }
  
  .contact-item {
    text-align: left;
  }
}
