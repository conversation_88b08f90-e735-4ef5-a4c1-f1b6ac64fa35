import React, { useState, useEffect } from 'react';
import { FaCode, FaServer, FaTools, FaReact, FaJs, FaHtml5, FaCss3Alt, FaNodeJs, FaPython, FaPhp, FaDatabase, FaGitAlt, FaDocker } from 'react-icons/fa';
import { skills } from '../data/portfolioData';
import './Skills.css';

const Skills = () => {
  const [activeCategory, setActiveCategory] = useState(0);
  const [hoveredSkill, setHoveredSkill] = useState(null);
  const [skillLevels, setSkillLevels] = useState({});
  const [animatedPercentages, setAnimatedPercentages] = useState({});
  const [animatedSkills, setAnimatedSkills] = useState(new Set()); // Track which skills have been animated

  // Icon mapping untuk skills
  const skillIcons = {
    'React': FaReact,
    'JavaScript': FaJs,
    'HTML5': FaHtml5,
    'CSS3': FaCss3Alt,
    'Node.js': FaNodeJs,
    'Python': FaPython,
    'PHP': FaPhp,
    'MySQL': FaDatabase,
    'MongoDB': FaDatabase,
    'PostgreSQL': FaDatabase,
    'Git': FaGitAlt,
    'Docker': FaDocker
  };

  const categoryIcons = {
    'Frontend': FaCode,
    'Backend': FaServer,
    'Tools & Others': FaTools
  };

  // Generate random skill levels on component mount
  useEffect(() => {
    const levels = {};
    skills.forEach(category => {
      category.items.forEach(skill => {
        levels[skill] = Math.floor(Math.random() * 20) + 80; // 80-100%
      });
    });
    setSkillLevels(levels);
  }, []);

  // Animate percentage when skill is hovered with easing (only once per skill)
  useEffect(() => {
    if (hoveredSkill && skillLevels[hoveredSkill] && !animatedSkills.has(hoveredSkill)) {
      const targetPercentage = skillLevels[hoveredSkill];
      const duration = 1500; // 1.5 seconds
      const startTime = Date.now();

      // Mark this skill as being animated
      setAnimatedSkills(prev => new Set([...prev, hoveredSkill]));

      // Easing function for smooth animation
      const easeOutCubic = (t) => 1 - Math.pow(1 - t, 3);

      const animateCounter = () => {
        const elapsed = Date.now() - startTime;
        const progress = Math.min(elapsed / duration, 1);
        const easedProgress = easeOutCubic(progress);
        const currentPercentage = Math.round(easedProgress * targetPercentage);

        setAnimatedPercentages(prev => ({
          ...prev,
          [hoveredSkill]: currentPercentage
        }));

        if (progress < 1) {
          requestAnimationFrame(animateCounter);
        }
      };

      requestAnimationFrame(animateCounter);
    }
  }, [hoveredSkill, skillLevels, animatedSkills]);

  // Reset animations when category changes
  useEffect(() => {
    setAnimatedSkills(new Set());
    setAnimatedPercentages({});
  }, [activeCategory]);

  return (
    <section id="skills" className="section skills">
      <div className="container">
        <h2 className="section-title">Skills & Technologies</h2>

        {/* Interactive Category Tabs */}
        <div className="skills-tabs">
          {skills.map((category, index) => {
            const IconComponent = categoryIcons[category.category] || FaCode;
            return (
              <button
                key={index}
                className={`skill-tab ${activeCategory === index ? 'active' : ''}`}
                onClick={() => setActiveCategory(index)}
              >
                <IconComponent className="tab-icon" />
                <span>{category.category}</span>
              </button>
            );
          })}
        </div>

        {/* Interactive Skills Display */}
        <div className="skills-interactive">
          <div className="active-category">
            <div className="category-header-interactive">
              <div className="category-icon-large">
                {React.createElement(categoryIcons[skills[activeCategory]?.category] || FaCode)}
              </div>
              <h3>{skills[activeCategory]?.category}</h3>
            </div>

            <div className="skills-grid-interactive">
              {skills[activeCategory]?.items.map((skill, skillIndex) => {
                const SkillIcon = skillIcons[skill] || FaCode;
                const level = skillLevels[skill] || 85;
                const animatedLevel = animatedPercentages[skill] || 0;
                const isAnimated = animatedSkills.has(skill);
                const isCurrentlyHovered = hoveredSkill === skill;

                // Show progress if skill has been animated or is currently being hovered
                const showProgress = isAnimated || isCurrentlyHovered;
                const progressWidth = showProgress ? level : 0;

                return (
                  <div
                    key={skillIndex}
                    className={`skill-card ${isCurrentlyHovered ? 'hovered' : ''} ${isAnimated ? 'animated' : ''}`}
                    onMouseEnter={() => setHoveredSkill(skill)}
                    onMouseLeave={() => setHoveredSkill(null)}
                  >
                    <div className="skill-icon-wrapper">
                      <SkillIcon className="skill-icon" />
                    </div>
                    <h4 className="skill-name">{skill}</h4>
                    <div className="skill-level">
                      <div className="skill-percentage">
                        <span className="percentage-number">{animatedLevel}</span>
                        <span className="percentage-symbol">%</span>
                      </div>
                      <div className="skill-bar-interactive">
                        <div
                          className={`skill-progress-interactive ${isAnimated ? 'completed' : ''}`}
                          style={{
                            width: `${progressWidth}%`,
                            animationDelay: `${skillIndex * 0.1}s`
                          }}
                        ></div>
                      </div>
                    </div>
                  </div>
                );
              })}
            </div>
          </div>
        </div>
        
        <div className="skills-summary">
          <div className="summary-card animate-fadeInUp">
            <h4>What I Bring to the Table</h4>
            <p>
              With a diverse skill set spanning both frontend and backend technologies,
              I create comprehensive digital solutions that are both visually appealing
              and functionally robust. My experience with modern frameworks and tools
              allows me to deliver high-quality, scalable applications.
            </p>


          </div>
        </div>
      </div>
    </section>
  );
};

export default Skills;
