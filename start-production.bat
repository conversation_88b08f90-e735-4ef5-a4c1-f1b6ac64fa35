@echo off
echo ================================
echo Building Portfolio for Production
echo ================================
call npm run build

if %errorlevel% neq 0 (
    echo Build failed! Please check for errors.
    pause
    exit /b 1
)

echo.
echo ================================
echo Starting Production Server...
echo ================================
echo Website: http://localhost:3000
echo Press Ctrl+C to stop server
echo ================================
echo.

REM Try serve first, fallback to node server
serve -s build -l 3000 2>nul || node server.js

pause
