.footer {
  background: linear-gradient(135deg, #0a0a0a 0%, #1a1a1a 100%);
  border-top: 1px solid rgba(255, 255, 255, 0.1);
  padding: 3rem 0 1rem;
  position: relative;
}

.footer::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-image: 
    radial-gradient(circle at 30% 50%, rgba(0, 212, 255, 0.03) 0%, transparent 50%),
    radial-gradient(circle at 70% 50%, rgba(255, 107, 107, 0.03) 0%, transparent 50%);
  pointer-events: none;
}

.footer-content {
  display: grid;
  grid-template-columns: 2fr 1fr 1fr;
  gap: 3rem;
  margin-bottom: 2rem;
  position: relative;
  z-index: 1;
}

.footer-section h3 {
  font-size: 1.8rem;
  font-weight: 700;
  background: linear-gradient(135deg, #00d4ff 0%, #ff6b6b 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  margin-bottom: 0.5rem;
}

.footer-section h4 {
  font-size: 1.2rem;
  font-weight: 600;
  color: #ffffff;
  margin-bottom: 1.5rem;
  position: relative;
}

.footer-section h4::after {
  content: '';
  position: absolute;
  bottom: -5px;
  left: 0;
  width: 30px;
  height: 2px;
  background: linear-gradient(135deg, #00d4ff 0%, #4ecdc4 100%);
}

.footer-logo p {
  color: #00d4ff;
  font-weight: 500;
  margin-bottom: 1rem;
}

.footer-description {
  color: #cccccc;
  line-height: 1.6;
  margin-bottom: 2rem;
}

.footer-social {
  display: flex;
  gap: 1rem;
}

.social-link {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 45px;
  height: 45px;
  background: rgba(255, 255, 255, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 50%;
  color: #ffffff;
  font-size: 1.1rem;
  text-decoration: none;
  transition: all 0.3s ease;
  backdrop-filter: blur(10px);
}

.social-link:hover {
  background: linear-gradient(135deg, #00d4ff 0%, #4ecdc4 100%);
  transform: translateY(-3px);
  box-shadow: 0 10px 25px rgba(0, 212, 255, 0.3);
}

.footer-links {
  display: flex;
  flex-direction: column;
  gap: 0.8rem;
}

.footer-link {
  color: #cccccc;
  text-decoration: none;
  transition: all 0.3s ease;
  position: relative;
  padding-left: 1rem;
}

.footer-link::before {
  content: '→';
  position: absolute;
  left: 0;
  color: #00d4ff;
  opacity: 0;
  transition: all 0.3s ease;
}

.footer-link:hover {
  color: #00d4ff;
  padding-left: 1.5rem;
}

.footer-link:hover::before {
  opacity: 1;
}

.contact-info {
  display: flex;
  flex-direction: column;
  gap: 0.8rem;
}

.contact-info p {
  color: #cccccc;
  margin: 0;
  transition: color 0.3s ease;
}

.contact-info p:hover {
  color: #00d4ff;
}

.footer-bottom {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding-top: 2rem;
  border-top: 1px solid rgba(255, 255, 255, 0.1);
  position: relative;
  z-index: 1;
}

.footer-copyright {
  color: #999999;
  font-size: 0.9rem;
}

.footer-copyright p {
  display: flex;
  align-items: center;
  gap: 0.3rem;
  margin: 0;
}

.heart-icon {
  color: #ff6b6b;
  animation: heartbeat 2s ease-in-out infinite;
}

@keyframes heartbeat {
  0%, 100% { transform: scale(1); }
  50% { transform: scale(1.1); }
}

.scroll-to-top {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 50px;
  height: 50px;
  background: linear-gradient(135deg, #00d4ff 0%, #4ecdc4 100%);
  border: none;
  border-radius: 50%;
  color: #ffffff;
  font-size: 1.2rem;
  cursor: pointer;
  transition: all 0.3s ease;
  box-shadow: 0 5px 15px rgba(0, 212, 255, 0.3);
}

.scroll-to-top:hover {
  transform: translateY(-3px);
  box-shadow: 0 10px 25px rgba(0, 212, 255, 0.4);
}

/* Mobile Responsive */
@media (max-width: 768px) {
  .footer {
    padding: 2rem 0 1rem;
  }
  
  .footer-content {
    grid-template-columns: 1fr;
    gap: 2rem;
    text-align: center;
  }
  
  .footer-section h3 {
    font-size: 1.5rem;
  }
  
  .footer-section h4 {
    font-size: 1.1rem;
  }
  
  .footer-section h4::after {
    left: 50%;
    transform: translateX(-50%);
  }
  
  .footer-social {
    justify-content: center;
  }
  
  .footer-bottom {
    flex-direction: column;
    gap: 1rem;
    text-align: center;
  }
  
  .footer-copyright {
    font-size: 0.8rem;
  }
}
