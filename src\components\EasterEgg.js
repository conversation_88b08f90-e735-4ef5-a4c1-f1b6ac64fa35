import React, { useState, useEffect } from 'react';
import { FaRocket, FaStar, FaHeart, FaGem, FaFire } from 'react-icons/fa';

const EasterEgg = () => {
  const [isActivated, setIsActivated] = useState(false);
  const [keySequence, setKeySequence] = useState([]);
  const [showMessage, setShowMessage] = useState(false);
  const [particles, setParticles] = useState([]);

  // Konami Code: ↑↑↓↓←→←→BA
  const konamiCode = [
    'ArrowUp', 'ArrowUp', 'ArrowDown', 'ArrowDown',
    'ArrowLeft', 'ArrowRight', 'ArrowLeft', 'ArrowRight',
    'KeyB', 'KeyA'
  ];

  useEffect(() => {
    const handleKeyDown = (e) => {
      const newSequence = [...keySequence, e.code].slice(-konamiCode.length);
      setKeySequence(newSequence);

      // Check if Konami code is entered
      if (JSON.stringify(newSequence) === JSON.stringify(konamiCode)) {
        activateEasterEgg();
        setKeySequence([]);
      }
    };

    window.addEventListener('keydown', handleKeyDown);
    return () => window.removeEventListener('keydown', handleKeyDown);
  }, [keySequence]);

  const activateEasterEgg = () => {
    setIsActivated(true);
    setShowMessage(true);
    createParticleExplosion();
    
    // Play celebration sound (if available)
    try {
      const audio = new Audio('data:audio/wav;base64,UklGRnoGAABXQVZFZm10IBAAAAABAAEAQB8AAEAfAAABAAgAZGF0YQoGAACBhYqFbF1fdJivrJBhNjVgodDbq2EcBj+a2/LDciUFLIHO8tiJNwgZaLvt559NEAxQp+PwtmMcBjiR1/LMeSwFJHfH8N2QQAoUXrTp66hVFApGn+DyvmwhBSuBzvLZiTYIG2m98OScTgwOUarm7blmGgU7k9n1unEiBC13yO/eizEIHWq+8+OWT');
      audio.play().catch(() => {});
    } catch (e) {}

    // Hide message after 5 seconds
    setTimeout(() => setShowMessage(false), 5000);
    
    // Deactivate after 10 seconds
    setTimeout(() => setIsActivated(false), 10000);
  };

  const createParticleExplosion = () => {
    const newParticles = [];
    for (let i = 0; i < 50; i++) {
      newParticles.push({
        id: Math.random(),
        x: Math.random() * window.innerWidth,
        y: Math.random() * window.innerHeight,
        vx: (Math.random() - 0.5) * 10,
        vy: (Math.random() - 0.5) * 10,
        color: ['#00d4ff', '#4ecdc4', '#ff6b6b', '#ffd93d', '#9b59b6'][Math.floor(Math.random() * 5)],
        size: Math.random() * 10 + 5,
        life: 1
      });
    }
    setParticles(newParticles);

    // Animate particles
    const animateParticles = () => {
      setParticles(prev => 
        prev.map(particle => ({
          ...particle,
          x: particle.x + particle.vx,
          y: particle.y + particle.vy,
          vy: particle.vy + 0.2, // gravity
          life: particle.life - 0.02,
          size: particle.size * 0.99
        })).filter(particle => particle.life > 0)
      );
    };

    const interval = setInterval(animateParticles, 16);
    setTimeout(() => clearInterval(interval), 3000);
  };

  // Secret developer mode
  const toggleDeveloperMode = () => {
    const body = document.body;
    if (body.style.filter === 'hue-rotate(180deg) saturate(2)') {
      body.style.filter = 'none';
    } else {
      body.style.filter = 'hue-rotate(180deg) saturate(2)';
    }
  };

  return (
    <>
      {/* Particles */}
      {particles.map(particle => (
        <div
          key={particle.id}
          style={{
            position: 'fixed',
            left: particle.x,
            top: particle.y,
            width: particle.size,
            height: particle.size,
            background: particle.color,
            borderRadius: '50%',
            pointerEvents: 'none',
            zIndex: 9999,
            opacity: particle.life,
            boxShadow: `0 0 ${particle.size}px ${particle.color}`
          }}
        />
      ))}

      {/* Easter Egg Message */}
      {showMessage && (
        <div style={{
          position: 'fixed',
          top: '50%',
          left: '50%',
          transform: 'translate(-50%, -50%)',
          background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
          color: 'white',
          padding: '2rem 3rem',
          borderRadius: '20px',
          textAlign: 'center',
          zIndex: 10000,
          boxShadow: '0 20px 40px rgba(0, 0, 0, 0.3)',
          border: '2px solid rgba(255, 255, 255, 0.2)',
          backdropFilter: 'blur(10px)',
          animation: 'bounceIn 0.5s ease'
        }}>
          <div style={{
            fontSize: '3rem',
            marginBottom: '1rem',
            display: 'flex',
            justifyContent: 'center',
            gap: '0.5rem'
          }}>
            <FaRocket style={{ animation: 'bounce 1s infinite' }} />
            <FaStar style={{ animation: 'bounce 1s infinite 0.1s' }} />
            <FaHeart style={{ animation: 'bounce 1s infinite 0.2s' }} />
            <FaGem style={{ animation: 'bounce 1s infinite 0.3s' }} />
            <FaFire style={{ animation: 'bounce 1s infinite 0.4s' }} />
          </div>
          <h2 style={{ margin: '0 0 1rem 0', fontSize: '2rem' }}>
            🎉 Konami Code Activated! 🎉
          </h2>
          <p style={{ margin: '0 0 1rem 0', fontSize: '1.2rem' }}>
            You found the secret! You're a true developer! 🚀
          </p>
          <button
            onClick={toggleDeveloperMode}
            style={{
              background: 'rgba(255, 255, 255, 0.2)',
              border: '1px solid rgba(255, 255, 255, 0.3)',
              color: 'white',
              padding: '0.8rem 1.5rem',
              borderRadius: '25px',
              cursor: 'pointer',
              fontSize: '1rem',
              fontWeight: '600',
              transition: 'all 0.3s ease'
            }}
            onMouseEnter={(e) => {
              e.target.style.background = 'rgba(255, 255, 255, 0.3)';
              e.target.style.transform = 'scale(1.05)';
            }}
            onMouseLeave={(e) => {
              e.target.style.background = 'rgba(255, 255, 255, 0.2)';
              e.target.style.transform = 'scale(1)';
            }}
          >
            Toggle Developer Mode
          </button>
        </div>
      )}

      {/* Activated Effects */}
      {isActivated && (
        <>
          {/* Rainbow border */}
          <div style={{
            position: 'fixed',
            top: 0,
            left: 0,
            right: 0,
            bottom: 0,
            border: '5px solid transparent',
            background: 'linear-gradient(45deg, #ff0000, #ff7f00, #ffff00, #00ff00, #0000ff, #4b0082, #9400d3) border-box',
            backgroundClip: 'padding-box, border-box',
            pointerEvents: 'none',
            zIndex: 9998,
            animation: 'rainbow-rotate 2s linear infinite'
          }} />

          {/* Floating icons */}
          {[...Array(10)].map((_, i) => (
            <div
              key={i}
              style={{
                position: 'fixed',
                left: `${Math.random() * 100}%`,
                top: `${Math.random() * 100}%`,
                fontSize: '2rem',
                color: ['#00d4ff', '#4ecdc4', '#ff6b6b', '#ffd93d', '#9b59b6'][i % 5],
                pointerEvents: 'none',
                zIndex: 9999,
                animation: `float ${2 + Math.random() * 2}s ease-in-out infinite`,
                animationDelay: `${Math.random() * 2}s`
              }}
            >
              {['🚀', '⭐', '💎', '🔥', '💫', '🌟', '✨', '🎉', '🎊', '🌈'][i]}
            </div>
          ))}
        </>
      )}



      {/* CSS Animations */}
      <style>
        {`
          @keyframes bounceIn {
            0% { transform: translate(-50%, -50%) scale(0.3); opacity: 0; }
            50% { transform: translate(-50%, -50%) scale(1.05); }
            70% { transform: translate(-50%, -50%) scale(0.9); }
            100% { transform: translate(-50%, -50%) scale(1); opacity: 1; }
          }
          
          @keyframes bounce {
            0%, 20%, 53%, 80%, 100% { transform: translateY(0); }
            40%, 43% { transform: translateY(-10px); }
            70% { transform: translateY(-5px); }
            90% { transform: translateY(-2px); }
          }
          
          @keyframes float {
            0%, 100% { transform: translateY(0px) rotate(0deg); }
            50% { transform: translateY(-20px) rotate(180deg); }
          }
          
          @keyframes rainbow-rotate {
            0% { filter: hue-rotate(0deg); }
            100% { filter: hue-rotate(360deg); }
          }
        `}
      </style>
    </>
  );
};

export default EasterEgg;
