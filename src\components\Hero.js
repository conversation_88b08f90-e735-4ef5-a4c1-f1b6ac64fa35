import React from 'react';
import { FaG<PERSON><PERSON>, Fa<PERSON>inkedin, FaInstagram, FaTwitter, FaDownload } from 'react-icons/fa';
import { personalInfo } from '../data/portfolioData';
import TypingAnimation from './TypingAnimation';
import './Hero.css';

const Hero = () => {
  const titles = [
    'Frontend Developer',
    'Backend Developer',
    'Full Stack Developer',
    'UI/UX Designer',
    'Problem Solver',
    'Code Enthusiast'
  ];

  const socialIcons = [
    { icon: FaGithub, link: personalInfo.social.github, name: 'GitHub' },
    { icon: FaLinkedin, link: personalInfo.social.linkedin, name: 'LinkedIn' },
    { icon: FaInstagram, link: personalInfo.social.instagram, name: 'Instagram' },
    { icon: FaTwitter, link: personalInfo.social.twitter, name: 'Twitter' }
  ];

  return (
    <section id="home" className="hero">
      <div className="hero-background">
        <div className="hero-particles"></div>
      </div>
      
      <div className="container">
        <div className="hero-content">
          <div className="hero-text">
            <h1 className="hero-name animate-fadeInUp">
              Hi, I'm <span className="gradient-text">{personalInfo.name}</span>
            </h1>
            
            <div className="hero-title animate-fadeInUp">
              <span className="title-prefix">I'm a </span>
              <TypingAnimation
                texts={titles}
                speed={120}
                deleteSpeed={80}
                pauseTime={2500}
                className="typing-text"
              />
            </div>
            
            <p className="hero-subtitle animate-fadeInUp">
              {personalInfo.subtitle}
            </p>
            
            <div className="hero-buttons animate-fadeInUp">
              <a href="#projects" className="btn-primary">
                View My Work
              </a>
              <a href={personalInfo.resumeLink} className="btn-secondary" target="_blank" rel="noopener noreferrer">
                <FaDownload /> Download CV
              </a>
            </div>
            
            <div className="hero-social animate-fadeInUp">
              {socialIcons.map((social, index) => (
                <a
                  key={index}
                  href={social.link}
                  target="_blank"
                  rel="noopener noreferrer"
                  className="social-link"
                  title={social.name}
                >
                  <social.icon />
                </a>
              ))}
            </div>
          </div>
          
          <div className="hero-image animate-fadeInRight">
            <div className="image-container">
              <img
                src="/images/profile.jpg"
                alt={personalInfo.name}
                className="profile-image"
              />
            </div>
          </div>
        </div>
        
        <div className="scroll-indicator">
          <div className="scroll-arrow"></div>
        </div>
      </div>
    </section>
  );
};

export default Hero;
