import { useState, useEffect } from 'react';
import { FaRocket, FaUser, FaCode, FaBriefcase, FaEnvelope } from 'react-icons/fa';

const ScrollProgress = () => {
  const [scrollProgress, setScrollProgress] = useState(0);
  const [activeSection, setActiveSection] = useState('hero');

  const sections = [
    { id: 'hero', name: 'Home', icon: FaRocket, color: '#00d4ff' },
    { id: 'about', name: 'About', icon: FaUser, color: '#4ecdc4' },
    { id: 'skills', name: 'Skills', icon: FaCode, color: '#ff6b6b' },
    { id: 'experience', name: 'Experience', icon: FaBriefcase, color: '#ffd93d' },
    { id: 'contact', name: 'Contact', icon: FaEnvelope, color: '#9b59b6' }
  ];

  useEffect(() => {
    const handleScroll = () => {
      const totalHeight = document.documentElement.scrollHeight - window.innerHeight;
      const progress = (window.scrollY / totalHeight) * 100;
      setScrollProgress(Math.min(100, Math.max(0, progress)));

      // Determine active section
      const sectionElements = sections.map(section => 
        document.getElementById(section.id)
      ).filter(Boolean);

      let currentSection = 'hero';
      sectionElements.forEach(element => {
        const rect = element.getBoundingClientRect();
        if (rect.top <= window.innerHeight / 2 && rect.bottom >= window.innerHeight / 2) {
          currentSection = element.id;
        }
      });
      setActiveSection(currentSection);
    };

    window.addEventListener('scroll', handleScroll);
    handleScroll(); // Initial call

    return () => window.removeEventListener('scroll', handleScroll);
  }, []);

  const scrollToSection = (sectionId) => {
    const element = document.getElementById(sectionId);
    if (element) {
      element.scrollIntoView({ behavior: 'smooth' });
    }
  };

  return (
    <>
      {/* Top Progress Bar */}
      <div style={{
        position: 'fixed',
        top: 0,
        left: 0,
        width: '100%',
        height: '4px',
        background: 'rgba(255, 255, 255, 0.1)',
        zIndex: 1000
      }}>
        <div style={{
          height: '100%',
          width: `${scrollProgress}%`,
          background: 'linear-gradient(90deg, #00d4ff, #4ecdc4, #ff6b6b, #ffd93d, #9b59b6)',
          transition: 'width 0.1s ease',
          boxShadow: '0 0 10px rgba(0, 212, 255, 0.5)'
        }} />
      </div>

      {/* Side Navigation */}
      <div style={{
        position: 'fixed',
        right: '2rem',
        top: '50%',
        transform: 'translateY(-50%)',
        zIndex: 1000,
        display: 'flex',
        flexDirection: 'column',
        gap: '1rem'
      }}>
        {sections.map((section, index) => {
          const IconComponent = section.icon;
          const isActive = activeSection === section.id;
          const progress = Math.min(100, Math.max(0, (scrollProgress - (index * 20)) * 5));
          
          return (
            <div
              key={section.id}
              onClick={() => scrollToSection(section.id)}
              style={{
                position: 'relative',
                width: '50px',
                height: '50px',
                borderRadius: '50%',
                background: isActive ? section.color : 'rgba(255, 255, 255, 0.1)',
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center',
                cursor: 'pointer',
                transition: 'all 0.3s ease',
                transform: isActive ? 'scale(1.2)' : 'scale(1)',
                boxShadow: isActive ? 
                  `0 0 20px ${section.color}80` : 
                  '0 2px 10px rgba(0, 0, 0, 0.2)',
                border: `2px solid ${isActive ? section.color : 'transparent'}`
              }}
              onMouseEnter={(e) => {
                if (!isActive) {
                  e.target.style.background = `${section.color}40`;
                  e.target.style.transform = 'scale(1.1)';
                }
              }}
              onMouseLeave={(e) => {
                if (!isActive) {
                  e.target.style.background = 'rgba(255, 255, 255, 0.1)';
                  e.target.style.transform = 'scale(1)';
                }
              }}
            >
              {/* Progress Ring */}
              <svg
                style={{
                  position: 'absolute',
                  top: '-2px',
                  left: '-2px',
                  width: '54px',
                  height: '54px',
                  transform: 'rotate(-90deg)'
                }}
              >
                <circle
                  cx="27"
                  cy="27"
                  r="25"
                  fill="none"
                  stroke="rgba(255, 255, 255, 0.1)"
                  strokeWidth="2"
                />
                <circle
                  cx="27"
                  cy="27"
                  r="25"
                  fill="none"
                  stroke={section.color}
                  strokeWidth="2"
                  strokeDasharray={`${2 * Math.PI * 25}`}
                  strokeDashoffset={`${2 * Math.PI * 25 * (1 - progress / 100)}`}
                  style={{
                    transition: 'stroke-dashoffset 0.3s ease',
                    filter: 'drop-shadow(0 0 5px currentColor)'
                  }}
                />
              </svg>

              <IconComponent 
                color={isActive ? 'white' : section.color} 
                size={18}
                style={{
                  filter: isActive ? 'drop-shadow(0 0 5px rgba(255,255,255,0.5))' : 'none'
                }}
              />

              {/* Tooltip */}
              <div style={{
                position: 'absolute',
                right: '60px',
                top: '50%',
                transform: 'translateY(-50%)',
                background: 'rgba(0, 0, 0, 0.8)',
                color: 'white',
                padding: '0.5rem 1rem',
                borderRadius: '20px',
                fontSize: '0.9rem',
                whiteSpace: 'nowrap',
                opacity: 0,
                transition: 'opacity 0.3s ease',
                pointerEvents: 'none',
                border: `1px solid ${section.color}40`
              }}
              className="nav-tooltip">
                {section.name}
              </div>

              {/* Active Indicator */}
              {isActive && (
                <div style={{
                  position: 'absolute',
                  right: '-10px',
                  top: '50%',
                  transform: 'translateY(-50%)',
                  width: '6px',
                  height: '6px',
                  background: section.color,
                  borderRadius: '50%',
                  animation: 'pulse 2s infinite'
                }} />
              )}
            </div>
          );
        })}
      </div>

      {/* Scroll Percentage Display */}
      <div style={{
        position: 'fixed',
        bottom: '2rem',
        left: '2rem',
        background: 'rgba(0, 0, 0, 0.8)',
        color: 'white',
        padding: '0.5rem 1rem',
        borderRadius: '20px',
        fontSize: '0.9rem',
        fontWeight: '600',
        zIndex: 1000,
        border: '1px solid rgba(0, 212, 255, 0.3)',
        backdropFilter: 'blur(10px)'
      }}>
        {Math.round(scrollProgress)}% scrolled
      </div>

      {/* CSS Animations */}
      <style>
        {`
          @keyframes pulse {
            0%, 100% { opacity: 1; transform: translateY(-50%) scale(1); }
            50% { opacity: 0.5; transform: translateY(-50%) scale(1.2); }
          }
          
          .nav-tooltip {
            opacity: 0 !important;
          }
          
          div:hover .nav-tooltip {
            opacity: 1 !important;
          }
        `}
      </style>
    </>
  );
};

export default ScrollProgress;
