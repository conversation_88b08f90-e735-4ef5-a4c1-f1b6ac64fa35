.experience {
  background: linear-gradient(135deg, #0a0a0a 0%, #1a1a1a 100%);
  position: relative;
}

.experience::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-image: 
    radial-gradient(circle at 80% 30%, rgba(78, 205, 196, 0.05) 0%, transparent 50%),
    radial-gradient(circle at 20% 70%, rgba(255, 107, 107, 0.05) 0%, transparent 50%);
  pointer-events: none;
}

.experience-content {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 4rem;
  margin-bottom: 4rem;
  position: relative;
  z-index: 1;
}

.experience-section {
  animation-delay: 0.2s;
}

.section-header {
  display: flex;
  align-items: center;
  gap: 1rem;
  margin-bottom: 2rem;
  padding-bottom: 1rem;
  border-bottom: 2px solid rgba(0, 212, 255, 0.2);
}

.section-icon {
  font-size: 2rem;
  color: #00d4ff;
}

.section-header h3 {
  font-size: 2rem;
  font-weight: 600;
  color: #ffffff;
}

.timeline {
  position: relative;
  padding-left: 2rem;
}

.timeline::before {
  content: '';
  position: absolute;
  left: 0;
  top: 0;
  bottom: 0;
  width: 2px;
  background: linear-gradient(to bottom, #00d4ff, #4ecdc4);
}

.timeline-item {
  position: relative;
  margin-bottom: 2.5rem;
  padding-left: 2rem;
}

.timeline-item:last-child {
  margin-bottom: 0;
}

.timeline-marker {
  position: absolute;
  left: -2rem;
  top: 0.5rem;
  width: 12px;
  height: 12px;
  background: linear-gradient(135deg, #00d4ff 0%, #4ecdc4 100%);
  border-radius: 50%;
  border: 3px solid #1a1a1a;
  z-index: 1;
}

.timeline-content {
  background: rgba(255, 255, 255, 0.05);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 15px;
  padding: 1.5rem;
  transition: all 0.3s ease;
}

.timeline-content:hover {
  background: rgba(255, 255, 255, 0.08);
  border-color: rgba(0, 212, 255, 0.3);
  transform: translateX(5px);
}

.timeline-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 0.5rem;
  flex-wrap: wrap;
  gap: 1rem;
}

.position {
  font-size: 1.2rem;
  font-weight: 600;
  color: #ffffff;
  margin: 0;
}

.duration {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  color: #00d4ff;
  font-size: 0.9rem;
  font-weight: 500;
}

.company {
  font-size: 1rem;
  font-weight: 500;
  color: #ff6b6b;
  margin: 0 0 1rem 0;
}

.description {
  font-size: 0.95rem;
  line-height: 1.6;
  color: #cccccc;
  margin: 0;
}

.experience-stats {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 2rem;
  position: relative;
  z-index: 1;
}

.stat-item {
  text-align: center;
  background: rgba(255, 255, 255, 0.05);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 20px;
  padding: 2rem;
  transition: all 0.3s ease;
  animation-delay: 0.4s;
}

.stat-item:hover {
  transform: translateY(-10px);
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
  border-color: rgba(0, 212, 255, 0.3);
}

.stat-number {
  font-size: 3rem;
  font-weight: 700;
  background: linear-gradient(135deg, #00d4ff 0%, #ff6b6b 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  margin-bottom: 0.5rem;
}

.stat-label {
  font-size: 1rem;
  color: #cccccc;
  font-weight: 500;
}

/* Mobile Responsive */
@media (max-width: 768px) {
  .experience-content {
    grid-template-columns: 1fr;
    gap: 2rem;
  }
  
  .section-header {
    flex-direction: column;
    text-align: center;
    gap: 0.5rem;
  }
  
  .section-header h3 {
    font-size: 1.5rem;
  }
  
  .timeline {
    padding-left: 1.5rem;
  }
  
  .timeline-item {
    padding-left: 1.5rem;
  }
  
  .timeline-marker {
    left: -1.5rem;
  }
  
  .timeline-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 0.5rem;
  }
  
  .position {
    font-size: 1.1rem;
  }
  
  .timeline-content {
    padding: 1.2rem;
  }
  
  .experience-stats {
    grid-template-columns: repeat(2, 1fr);
    gap: 1rem;
  }
  
  .stat-item {
    padding: 1.5rem;
  }
  
  .stat-number {
    font-size: 2.5rem;
  }
}
