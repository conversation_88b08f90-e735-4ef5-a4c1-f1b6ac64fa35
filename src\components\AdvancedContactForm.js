import { useState } from 'react';
import { <PERSON><PERSON><PERSON><PERSON>, FaEnvelope, FaComment, FaPaperPlane, FaCheck, FaTimes } from 'react-icons/fa';

const AdvancedContactForm = () => {
  const [formData, setFormData] = useState({
    name: '',
    email: '',
    subject: '',
    message: ''
  });
  const [errors, setErrors] = useState({});
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [submitStatus, setSubmitStatus] = useState(null);

  const validateField = (name, value) => {
    switch (name) {
      case 'name':
        return value.length < 2 ? 'Name must be at least 2 characters' : '';
      case 'email':
        const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
        return !emailRegex.test(value) ? 'Please enter a valid email' : '';
      case 'subject':
        return value.length < 5 ? 'Subject must be at least 5 characters' : '';
      case 'message':
        return value.length < 10 ? 'Message must be at least 10 characters' : '';
      default:
        return '';
    }
  };

  const handleChange = (e) => {
    const { name, value } = e.target;
    setFormData(prev => ({ ...prev, [name]: value }));
    
    // Real-time validation
    const error = validateField(name, value);
    setErrors(prev => ({ ...prev, [name]: error }));
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    setIsSubmitting(true);

    // Validate all fields
    const newErrors = {};
    Object.keys(formData).forEach(key => {
      const error = validateField(key, formData[key]);
      if (error) newErrors[key] = error;
    });

    if (Object.keys(newErrors).length > 0) {
      setErrors(newErrors);
      setIsSubmitting(false);
      return;
    }

    // Simulate API call
    try {
      await new Promise(resolve => setTimeout(resolve, 2000));
      setSubmitStatus('success');
      setFormData({ name: '', email: '', subject: '', message: '' });
      setErrors({});
    } catch (error) {
      setSubmitStatus('error');
    } finally {
      setIsSubmitting(false);
      setTimeout(() => setSubmitStatus(null), 5000);
    }
  };

  const getFieldIcon = (fieldName) => {
    const icons = {
      name: FaUser,
      email: FaEnvelope,
      subject: FaComment,
      message: FaComment
    };
    return icons[fieldName] || FaUser;
  };

  return (
    <form onSubmit={handleSubmit} style={{
      background: 'rgba(255, 255, 255, 0.05)',
      backdropFilter: 'blur(10px)',
      border: '1px solid rgba(255, 255, 255, 0.1)',
      borderRadius: '20px',
      padding: '2rem',
      position: 'relative',
      overflow: 'hidden'
    }}>
      {/* Background Effect */}
      <div style={{
        position: 'absolute',
        top: 0,
        left: 0,
        width: '100%',
        height: '100%',
        background: 'linear-gradient(135deg, rgba(0, 212, 255, 0.05) 0%, rgba(78, 205, 196, 0.05) 100%)',
        pointerEvents: 'none'
      }} />

      <div style={{ position: 'relative', zIndex: 1 }}>
        {/* Form Fields */}
        {Object.keys(formData).map((fieldName) => {
          const IconComponent = getFieldIcon(fieldName);
          const isTextarea = fieldName === 'message';
          const hasError = errors[fieldName];
          const hasValue = formData[fieldName].length > 0;

          return (
            <div key={fieldName} style={{
              marginBottom: '1.5rem',
              position: 'relative'
            }}>
              <div style={{
                position: 'relative',
                display: 'flex',
                alignItems: isTextarea ? 'flex-start' : 'center',
                background: 'rgba(255, 255, 255, 0.05)',
                border: `2px solid ${hasError ? '#ff6b6b' : hasValue ? '#4ecdc4' : 'rgba(255, 255, 255, 0.1)'}`,
                borderRadius: '12px',
                padding: '1rem',
                transition: 'all 0.3s ease'
              }}>
                <IconComponent 
                  style={{
                    color: hasError ? '#ff6b6b' : hasValue ? '#4ecdc4' : '#999',
                    marginRight: '1rem',
                    marginTop: isTextarea ? '0.2rem' : '0',
                    transition: 'color 0.3s ease'
                  }}
                />
                
                {isTextarea ? (
                  <textarea
                    name={fieldName}
                    value={formData[fieldName]}
                    onChange={handleChange}
                    placeholder={`Your ${fieldName}...`}
                    rows={4}
                    style={{
                      flex: 1,
                      background: 'transparent',
                      border: 'none',
                      outline: 'none',
                      color: '#fff',
                      fontSize: '1rem',
                      resize: 'vertical',
                      minHeight: '100px'
                    }}
                  />
                ) : (
                  <input
                    type={fieldName === 'email' ? 'email' : 'text'}
                    name={fieldName}
                    value={formData[fieldName]}
                    onChange={handleChange}
                    placeholder={`Your ${fieldName}...`}
                    style={{
                      flex: 1,
                      background: 'transparent',
                      border: 'none',
                      outline: 'none',
                      color: '#fff',
                      fontSize: '1rem'
                    }}
                  />
                )}

                {/* Validation Icon */}
                {hasValue && (
                  <div style={{ marginLeft: '0.5rem' }}>
                    {hasError ? (
                      <FaTimes color="#ff6b6b" />
                    ) : (
                      <FaCheck color="#4ecdc4" />
                    )}
                  </div>
                )}
              </div>

              {/* Error Message */}
              {hasError && (
                <div style={{
                  color: '#ff6b6b',
                  fontSize: '0.8rem',
                  marginTop: '0.5rem',
                  marginLeft: '1rem',
                  animation: 'shake 0.5s ease-in-out'
                }}>
                  {hasError}
                </div>
              )}
            </div>
          );
        })}

        {/* Submit Button */}
        <button
          type="submit"
          disabled={isSubmitting || Object.keys(errors).some(key => errors[key])}
          style={{
            width: '100%',
            background: isSubmitting 
              ? 'linear-gradient(135deg, #999 0%, #666 100%)'
              : 'linear-gradient(135deg, #00d4ff 0%, #4ecdc4 100%)',
            border: 'none',
            borderRadius: '12px',
            padding: '1rem 2rem',
            color: '#fff',
            fontSize: '1.1rem',
            fontWeight: '600',
            cursor: isSubmitting ? 'not-allowed' : 'pointer',
            transition: 'all 0.3s ease',
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center',
            gap: '0.5rem',
            position: 'relative',
            overflow: 'hidden'
          }}
        >
          {isSubmitting ? (
            <>
              <div style={{
                width: '20px',
                height: '20px',
                border: '2px solid rgba(255,255,255,0.3)',
                borderTop: '2px solid #fff',
                borderRadius: '50%',
                animation: 'spin 1s linear infinite'
              }} />
              Sending...
            </>
          ) : (
            <>
              <FaPaperPlane />
              Send Message
            </>
          )}
        </button>

        {/* Status Messages */}
        {submitStatus && (
          <div style={{
            marginTop: '1rem',
            padding: '1rem',
            borderRadius: '12px',
            textAlign: 'center',
            background: submitStatus === 'success' 
              ? 'rgba(78, 205, 196, 0.2)' 
              : 'rgba(255, 107, 107, 0.2)',
            border: `1px solid ${submitStatus === 'success' ? '#4ecdc4' : '#ff6b6b'}`,
            color: submitStatus === 'success' ? '#4ecdc4' : '#ff6b6b',
            animation: 'slideIn 0.5s ease'
          }}>
            {submitStatus === 'success' ? (
              <>
                <FaCheck style={{ marginRight: '0.5rem' }} />
                Message sent successfully! I'll get back to you soon.
              </>
            ) : (
              <>
                <FaTimes style={{ marginRight: '0.5rem' }} />
                Failed to send message. Please try again.
              </>
            )}
          </div>
        )}
      </div>

      <style>
        {`
          @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
          }
          
          @keyframes shake {
            0%, 100% { transform: translateX(0); }
            25% { transform: translateX(-5px); }
            75% { transform: translateX(5px); }
          }
          
          @keyframes slideIn {
            0% { opacity: 0; transform: translateY(20px); }
            100% { opacity: 1; transform: translateY(0); }
          }
        `}
      </style>
    </form>
  );
};

export default AdvancedContactForm;
