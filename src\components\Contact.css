.contact {
  background: linear-gradient(135deg, #1a1a1a 0%, #0a0a0a 100%);
  position: relative;
}

.contact::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-image: 
    radial-gradient(circle at 20% 80%, rgba(0, 212, 255, 0.05) 0%, transparent 50%),
    radial-gradient(circle at 80% 20%, rgba(255, 107, 107, 0.05) 0%, transparent 50%);
  pointer-events: none;
}

.contact-content {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 4rem;
  position: relative;
  z-index: 1;
}

.contact-info {
  animation-delay: 0.2s;
}

.contact-intro {
  margin-bottom: 3rem;
}

.contact-intro h3 {
  font-size: 2rem;
  font-weight: 600;
  color: #ffffff;
  margin-bottom: 1rem;
}

.contact-intro p {
  font-size: 1.1rem;
  line-height: 1.7;
  color: #cccccc;
}

.contact-details {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
}

.contact-item {
  display: flex;
  align-items: center;
  gap: 1.5rem;
  padding: 1.5rem;
  background: rgba(255, 255, 255, 0.05);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 15px;
  text-decoration: none;
  transition: all 0.3s ease;
}

.contact-item:hover {
  background: rgba(0, 212, 255, 0.1);
  border-color: rgba(0, 212, 255, 0.3);
  transform: translateX(10px);
}

.contact-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 60px;
  height: 60px;
  background: linear-gradient(135deg, #00d4ff 0%, #4ecdc4 100%);
  border-radius: 50%;
  color: #ffffff;
  font-size: 1.5rem;
  flex-shrink: 0;
}

.contact-text h4 {
  font-size: 1.2rem;
  font-weight: 600;
  color: #ffffff;
  margin-bottom: 0.3rem;
}

.contact-text p {
  font-size: 1rem;
  color: #cccccc;
  margin: 0;
}

.contact-form-container {
  animation-delay: 0.4s;
}

.contact-form {
  background: rgba(255, 255, 255, 0.05);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 20px;
  padding: 2.5rem;
}

.form-group {
  margin-bottom: 1.5rem;
}

/* Submit Status Messages */
.submit-message {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 1rem 1.5rem;
  border-radius: 10px;
  margin-bottom: 1.5rem;
  font-weight: 500;
  animation: slideDown 0.3s ease-out;
}

.submit-message.success {
  background: rgba(40, 167, 69, 0.2);
  border: 1px solid rgba(40, 167, 69, 0.5);
  color: #28a745;
}

.submit-message.error {
  background: rgba(220, 53, 69, 0.2);
  border: 1px solid rgba(220, 53, 69, 0.5);
  color: #dc3545;
}

@keyframes slideDown {
  from {
    opacity: 0;
    transform: translateY(-20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.form-input {
  width: 100%;
  padding: 1rem 1.5rem;
  background: rgba(255, 255, 255, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 10px;
  color: #ffffff;
  font-size: 1rem;
  transition: all 0.3s ease;
  backdrop-filter: blur(10px);
  position: relative;
}

.form-input.focused {
  transform: translateY(-2px);
  box-shadow: 0 10px 25px rgba(0, 212, 255, 0.2);
}

.form-input.error {
  border-color: #dc3545;
  background: rgba(220, 53, 69, 0.1);
}

.error-message {
  display: block;
  color: #dc3545;
  font-size: 0.8rem;
  margin-top: 0.5rem;
  margin-left: 0.5rem;
  animation: fadeIn 0.3s ease-out;
}

@keyframes fadeIn {
  from { opacity: 0; }
  to { opacity: 1; }
}

.form-input::placeholder {
  color: #999999;
}

.form-input:focus {
  outline: none;
  border-color: #00d4ff;
  background: rgba(0, 212, 255, 0.1);
  box-shadow: 0 0 20px rgba(0, 212, 255, 0.2);
}

.form-textarea {
  resize: vertical;
  min-height: 120px;
  font-family: inherit;
}

.form-submit {
  width: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
  font-size: 1.1rem;
  font-weight: 600;
  padding: 1.2rem;
  margin-top: 1rem;
  border: none;
  cursor: pointer;
  position: relative;
  overflow: hidden;
}

.form-submit:hover:not(.loading) {
  transform: translateY(-3px);
  box-shadow: 0 15px 35px rgba(0, 212, 255, 0.4);
}

.form-submit.loading {
  opacity: 0.8;
  cursor: not-allowed;
  transform: none;
}

.spinner {
  width: 20px;
  height: 20px;
  border: 2px solid transparent;
  border-top: 2px solid #ffffff;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.form-submit::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  transition: left 0.5s ease;
}

.form-submit:hover::before {
  left: 100%;
}

/* Form Animation */
.form-input {
  position: relative;
}

.form-input:focus {
  animation: inputFocus 0.3s ease;
}

@keyframes inputFocus {
  0% { transform: scale(1); }
  50% { transform: scale(1.02); }
  100% { transform: scale(1); }
}

/* Loading State */
.form-submit.loading {
  opacity: 0.7;
  cursor: not-allowed;
}

.form-submit.loading::after {
  content: '';
  width: 20px;
  height: 20px;
  border: 2px solid transparent;
  border-top: 2px solid #ffffff;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-left: 10px;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* Mobile Responsive */
@media (max-width: 768px) {
  .contact-content {
    grid-template-columns: 1fr;
    gap: 2rem;
  }
  
  .contact-intro h3 {
    font-size: 1.5rem;
  }
  
  .contact-intro p {
    font-size: 1rem;
  }
  
  .contact-item {
    padding: 1.2rem;
    gap: 1rem;
  }
  
  .contact-icon {
    width: 50px;
    height: 50px;
    font-size: 1.2rem;
  }
  
  .contact-text h4 {
    font-size: 1.1rem;
  }
  
  .contact-form {
    padding: 2rem;
  }
  
  .form-input {
    padding: 0.8rem 1.2rem;
  }
  
  .form-submit {
    padding: 1rem;
    font-size: 1rem;
  }
}
