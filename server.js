const express = require('express');
const path = require('path');
const app = express();
const port = process.env.PORT || 3000;

// Serve static files from the React app build directory
app.use(express.static(path.join(__dirname, 'build')));

// Handle React routing, return all requests to React app
app.get('*', function(req, res) {
  res.sendFile(path.join(__dirname, 'build', 'index.html'));
});

app.listen(port, function() {
  console.log('=================================');
  console.log('🚀 Portfolio Server Started!');
  console.log('=================================');
  console.log(`📱 Local: http://localhost:${port}`);
  console.log(`🌐 Network: http://192.168.1.x:${port}`);
  console.log('=================================');
  console.log('Press Ctrl+C to stop server');
});

module.exports = app;
