import React, { useState, useRef } from 'react';
import { FaUser, FaMapMarkerAlt, FaEnvelope, FaPhone } from 'react-icons/fa';
import { personalInfo } from '../data/portfolioData';
import './About.css';

const About = () => {
  const [mousePosition, setMousePosition] = useState({ x: 0, y: 0 });
  const [isHovered, setIsHovered] = useState(false);
  const imageRef = useRef(null);
  const contactInfo = [
    { icon: FaMapMarkerAlt, label: 'Location', value: personalInfo.location },
    { icon: FaEnvelope, label: 'Email', value: personalInfo.email },
    { icon: FaPhone, label: 'Phone', value: personalInfo.phone }
  ];

  // Handle mouse movement for 3D effect
  const handleMouseMove = (e) => {
    if (!imageRef.current) return;

    const rect = imageRef.current.getBoundingClientRect();
    const centerX = rect.left + rect.width / 2;
    const centerY = rect.top + rect.height / 2;

    const mouseX = e.clientX - centerX;
    const mouseY = e.clientY - centerY;

    // Calculate rotation values (limit the rotation)
    const rotateX = (mouseY / rect.height) * -20; // Max 20 degrees
    const rotateY = (mouseX / rect.width) * 20;   // Max 20 degrees

    setMousePosition({ x: rotateY, y: rotateX });
  };

  const handleMouseEnter = () => {
    setIsHovered(true);
  };

  const handleMouseLeave = () => {
    setIsHovered(false);
    setMousePosition({ x: 0, y: 0 });
  };

  return (
    <section id="about" className="section about">
      <div className="container">
        <h2 className="section-title">About Me</h2>
        
        <div className="about-content">
          <div className="about-text animate-fadeInLeft">
            <div className="about-intro">
              <FaUser className="about-icon" />
              <h3>Get to know me!</h3>
            </div>
            
            <p className="about-description">
              {personalInfo.about}
            </p>
            
            <div className="about-details">
              {contactInfo.map((info, index) => (
                <div key={index} className="contact-item">
                  <info.icon className="contact-icon" />
                  <div className="contact-info">
                    <span className="contact-label">{info.label}</span>
                    <span className="contact-value">{info.value}</span>
                  </div>
                </div>
              ))}
            </div>
            
            <div className="about-buttons">
              <a href="#contact" className="btn-primary">
                Let's Talk
              </a>
              <a href={personalInfo.resumeLink} className="btn-secondary" target="_blank" rel="noopener noreferrer">
                Download CV
              </a>
            </div>
          </div>
          
          <div className="about-image animate-fadeInRight">
            <div
              className={`image-wrapper-3d ${isHovered ? 'hovered' : ''}`}
              ref={imageRef}
              onMouseMove={handleMouseMove}
              onMouseEnter={handleMouseEnter}
              onMouseLeave={handleMouseLeave}
              style={{
                transform: `perspective(1000px) rotateX(${mousePosition.y}deg) rotateY(${mousePosition.x}deg)`,
                transition: isHovered ? 'none' : 'transform 0.5s ease-out'
              }}
            >
              {/* 3D Container - Simplified */}
              <div className="photo-3d-container">
                {/* Main Photo */}
                <div className="photo-face front">
                  <img
                    src="/images/profile.jpg"
                    alt={personalInfo.name}
                    className="about-photo-img"
                  />
                  <div className="photo-overlay"></div>
                </div>
              </div>

              {/* Floating Elements */}
              <div className="floating-elements">
                <div className="floating-circle circle-1"></div>
                <div className="floating-circle circle-2"></div>
                <div className="floating-circle circle-3"></div>
              </div>

              {/* Light Effect */}
              <div className="light-effect"></div>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
};

export default About;
