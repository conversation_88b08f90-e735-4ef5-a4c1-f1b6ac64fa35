# 📝 PANDUAN LENGKAP EDIT PORTFOLIO

## 🎯 FILE UTAMA YANG PERLU DIEDIT

### 1. `src/data/portfolioData.js` - FILE TERPENTING!

Ini adalah file utama yang berisi semua data portfolio Anda. Edit bagian-bagian berikut:

#### A. INFORMASI PERSONAL
```javascript
export const personalInfo = {
  name: "GANTI DENGAN NAMA ANDA",                    // Contoh: "John Doe"
  title: "GANTI DENGAN PROFESI ANDA",                // Contoh: "Full Stack Developer"
  subtitle: "GANTI DENGAN DESKRIPSI SINGKAT",        // Contoh: "Passionate about creating amazing digital experiences"
  email: "<EMAIL>",                  // Contoh: "<EMAIL>"
  phone: "+62 831 2375 7272",               // Contoh: "+62 812 3456 7890"
  location: "GANTI DENGAN LOKASI ANDA",              // Contoh: "Jakarta, Indonesia"
  
  // Social Media Links - GANTI DENGAN LINK ANDA
  social: {
    github: "https://github.com/username-anda",
    linkedin: "https://linkedin.com/in/username-anda",
    instagram: "https://instagram.com/username-anda",
    twitter: "https://twitter.com/username-anda"
  },
  
  // About Me - GANTI DENGAN DESKRIPSI LENGKAP TENTANG ANDA
  about: `Tulis deskripsi lengkap tentang diri Anda di sini. 
          Ceritakan pengalaman, passion, dan keahlian Anda.
          Bisa beberapa paragraf.`,
  
  // Resume/CV Link - GANTI DENGAN LINK CV ANDA
  resumeLink: "https://drive.google.com/file/d/your-cv-link"
};
```

#### B. SKILLS/KEAHLIAN
```javascript
export const skills = [
  {
    category: "Frontend",
    items: ["React", "JavaScript", "HTML5", "CSS3", "Vue.js"] // GANTI SESUAI SKILL ANDA
  },
  {
    category: "Backend",
    items: ["Node.js", "Python", "PHP", "MySQL", "MongoDB"] // GANTI SESUAI SKILL ANDA
  },
  {
    category: "Tools & Others",
    items: ["Git", "Docker", "AWS", "Figma", "Photoshop"] // GANTI SESUAI SKILL ANDA
  }
  // BISA TAMBAH KATEGORI LAIN:
  // {
  //   category: "Mobile",
  //   items: ["React Native", "Flutter", "Android"]
  // }
];
```

#### C. PROJECTS/PORTFOLIO
```javascript
export const projects = [
  {
    id: 1,
    title: "NAMA PROJECT ANDA",                       // Contoh: "E-Commerce Website"
    description: "DESKRIPSI PROJECT ANDA",            // Jelaskan apa yang dibuat
    image: "/images/project1.jpg",                    // Path ke gambar (letakkan di public/images/)
    technologies: ["React", "Node.js", "MongoDB"],    // Teknologi yang digunakan
    liveLink: "https://link-demo-project.com",        // Link demo/live project
    githubLink: "https://github.com/user/project",    // Link GitHub
    featured: true                                     // true = tampil di featured, false = tidak
  },
  {
    id: 2,
    title: "PROJECT KEDUA",
    description: "Deskripsi project kedua...",
    image: "/images/project2.jpg",
    technologies: ["Vue.js", "Laravel", "MySQL"],
    liveLink: "https://project2.com",
    githubLink: "https://github.com/user/project2",
    featured: false
  }
  // TAMBAH PROJECT LAIN DENGAN FORMAT YANG SAMA
];
```

#### D. PENGALAMAN KERJA
```javascript
export const experience = [
  {
    id: 1,
    company: "NAMA PERUSAHAAN",                       // Contoh: "PT. Tech Indonesia"
    position: "POSISI/JABATAN",                       // Contoh: "Frontend Developer"
    duration: "PERIODE KERJA",                        // Contoh: "2022 - Present"
    description: "DESKRIPSI PEKERJAAN DAN TANGGUNG JAWAB"
  },
  {
    id: 2,
    company: "PERUSAHAAN SEBELUMNYA",
    position: "POSISI SEBELUMNYA",
    duration: "2020 - 2022",
    description: "Deskripsi pekerjaan sebelumnya..."
  }
  // TAMBAH PENGALAMAN LAIN
];
```

#### E. PENDIDIKAN
```javascript
export const education = [
  {
    id: 1,
    institution: "NAMA UNIVERSITAS/SEKOLAH",          // Contoh: "Universitas Indonesia"
    degree: "GELAR/JURUSAN",                          // Contoh: "S1 Teknik Informatika"
    duration: "PERIODE",                              // Contoh: "2018 - 2022"
    description: "DESKRIPSI TAMBAHAN (IPK, prestasi, dll)"
  }
  // TAMBAH PENDIDIKAN LAIN JIKA ADA
];
```

## 📸 MENAMBAHKAN GAMBAR

### 1. Buat folder `images` di `public/`
```
public/
├── images/
│   ├── profile.jpg          ← Foto profil Anda
│   ├── about-photo.jpg      ← Foto untuk section About
│   ├── project1.jpg         ← Screenshot project 1
│   ├── project2.jpg         ← Screenshot project 2
│   └── project3.jpg         ← Screenshot project 3
└── index.html
```

### 2. Update path gambar di `portfolioData.js`
```javascript
// Untuk project
image: "/images/project1.jpg"  // Sesuaikan dengan nama file Anda
```

### 3. Ganti placeholder foto
- Di Hero section: Edit `src/components/Hero.js` line yang ada "Your Photo"
- Di About section: Edit `src/components/About.js` line yang ada "Your Professional Photo"

## 🎨 MENGUBAH WARNA TEMA

Di file `src/data/portfolioData.js`, bagian bawah:

```javascript
export const theme = {
  primary: "#00d4ff",      // Warna utama (biru) - ganti sesuai selera
  secondary: "#ff6b6b",    // Warna sekunder (merah) - ganti sesuai selera  
  accent: "#4ecdc4",       // Warna aksen (hijau) - ganti sesuai selera
  background: "#0a0a0a",   // Warna background
  surface: "#1a1a1a",      // Warna surface
  text: "#ffffff"          // Warna text
};
```

Contoh warna lain:
- Biru: `#007bff`
- Hijau: `#28a745`
- Ungu: `#6f42c1`
- Orange: `#fd7e14`

## 🔧 CUSTOMIZATION LANJUTAN

### 1. Mengubah Font
Edit `public/index.html`:
```html
<link href="https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700&display=swap" rel="stylesheet">
```

Lalu edit `src/index.css`:
```css
font-family: 'Poppins', sans-serif;
```

### 2. Menambah Section Baru
1. Buat komponen baru di `src/components/`
2. Import dan tambahkan di `src/App.js`

### 3. Mengubah Animasi
Edit file CSS masing-masing komponen untuk mengubah animasi.

## ✅ CHECKLIST SEBELUM PUBLISH

- [ ] Ganti semua informasi personal
- [ ] Upload foto profil dan project
- [ ] Test semua link (social media, project, CV)
- [ ] Cek responsive di mobile
- [ ] Test contact form
- [ ] Update meta tags di `public/index.html`

## 🚀 CARA MENJALANKAN

1. Buka terminal/command prompt
2. Masuk ke folder project: `cd c:\xampp\htdocs\portofolio`
3. Install dependencies: `npm install`
4. Jalankan: `npm start`
5. Buka browser: `http://localhost:3000`

## 📞 BUTUH BANTUAN?

Jika ada yang tidak jelas atau error, silakan hubungi saya!

---
**Happy coding! 🎉**
