import React, { useState, useEffect } from 'react';
import { FaBriefcase, FaGraduationCap, FaCalendarAlt, FaMapMarkerAlt, FaTimes, FaExternalLinkAlt, FaEye, FaStar, FaAward, FaCode, FaUsers, FaChevronRight, FaPlay, FaPause } from 'react-icons/fa';

// Hard-coded data untuk testing
const experienceData = [
  {
    id: 1,
    company: "Tech Company",
    position: "Senior Frontend Developer",
    duration: "2022 - Present",
    location: "Jakarta, Indonesia",
    type: "Full-time",
    description: "Mengembangkan dan memelihara aplikasi web menggunakan React dan TypeScript. Memimpin tim frontend dan berkolaborasi dengan tim backend untuk mengoptimalkan performa aplikasi.",
    responsibilities: [
      "Memimpin tim frontend dengan 5 developer",
      "Mengoptimalkan performa aplikasi hingga 40%",
      "Implementasi CI/CD pipeline",
      "Code review dan mentoring junior developer"
    ],
    technologies: ["React", "TypeScript", "Node.js", "Docker", "AWS"],
    achievements: [
      "Meningkatkan loading time aplikasi dari 3s menjadi 1.2s",
      "Implementasi testing coverage hingga 85%",
      "Mengurangi bug production hingga 60%"
    ]
  },
  {
    id: 2,
    company: "Digital Agency",
    position: "Full Stack Developer",
    duration: "2020 - 2022",
    location: "Bandung, Indonesia",
    type: "Full-time",
    description: "Membangun website dan aplikasi web untuk berbagai klien menggunakan berbagai teknologi seperti React, Node.js, dan PHP.",
    responsibilities: [
      "Mengembangkan 15+ website untuk klien",
      "Maintenance dan support aplikasi existing",
      "Kolaborasi dengan tim design dan marketing",
      "Training client untuk penggunaan CMS"
    ],
    technologies: ["React", "PHP", "MySQL", "WordPress", "Laravel"],
    achievements: [
      "Berhasil menyelesaikan 20+ project tepat waktu",
      "Client satisfaction rate 95%",
      "Mengembangkan custom CMS untuk 5 klien"
    ]
  },
  {
    id: 3,
    company: "Startup Company",
    position: "Junior Developer",
    duration: "2019 - 2020",
    location: "Jakarta, Indonesia",
    type: "Full-time",
    description: "Memulai karir sebagai developer dengan fokus pada pengembangan frontend menggunakan HTML, CSS, JavaScript, dan React.",
    responsibilities: [
      "Mengembangkan UI/UX untuk aplikasi mobile",
      "Bug fixing dan testing aplikasi",
      "Dokumentasi code dan API",
      "Learning dan development skill"
    ],
    technologies: ["HTML", "CSS", "JavaScript", "React", "Git"],
    achievements: [
      "Menguasai React dalam 3 bulan",
      "Berkontribusi pada 10+ feature baru",
      "Mendapat promosi dalam 8 bulan"
    ]
  }
];

const educationData = [
  {
    id: 1,
    institution: "Universitas Indonesia",
    degree: "S1 Teknik Informatika",
    duration: "2015 - 2019",
    location: "Depok, Indonesia",
    type: "Bachelor's Degree",
    description: "Fokus pada pengembangan software dan algoritma. IPK: 3.75",
    courses: [
      "Data Structures & Algorithms",
      "Database Management Systems",
      "Software Engineering",
      "Computer Networks",
      "Artificial Intelligence"
    ],
    achievements: [
      "IPK: 3.75/4.00",
      "Juara 2 Programming Contest 2018",
      "Ketua Himpunan Mahasiswa Informatika",
      "Best Final Project Award"
    ],
    finalProject: "Sistem Rekomendasi E-commerce menggunakan Machine Learning"
  }
];

const ExperienceCards = () => {
  const [selectedItem, setSelectedItem] = useState(null);
  const [activeTab, setActiveTab] = useState('experience');
  const [hoveredCard, setHoveredCard] = useState(null);
  const [animatingCards, setAnimatingCards] = useState([]);
  const [isAutoPlay, setIsAutoPlay] = useState(false);
  const [currentAutoIndex, setCurrentAutoIndex] = useState(0);

  // Auto-play functionality
  useEffect(() => {
    let interval;
    if (isAutoPlay) {
      const currentData = activeTab === 'experience' ? experienceData : educationData;
      interval = setInterval(() => {
        setCurrentAutoIndex((prev) => (prev + 1) % currentData.length);
      }, 3000);
    }
    return () => clearInterval(interval);
  }, [isAutoPlay, activeTab]);

  // Animate cards on load
  useEffect(() => {
    const currentData = activeTab === 'experience' ? experienceData : educationData;
    setAnimatingCards([]);
    currentData.forEach((_, index) => {
      setTimeout(() => {
        setAnimatingCards(prev => [...prev, index]);
      }, index * 200);
    });
  }, [activeTab]);

  return (
    <>
      {/* CSS Animations */}
      <style>
        {`
          @keyframes pulse {
            0%, 100% { transform: translateY(-8px) scale(1.01); }
            50% { transform: translateY(-12px) scale(1.02); }
          }

          @keyframes particle-float {
            0%, 100% { transform: translate(-50%, -50%) scale(1); opacity: 1; }
            25% { transform: translate(-30%, -70%) scale(1.2); opacity: 0.8; }
            50% { transform: translate(-70%, -30%) scale(0.8); opacity: 0.6; }
            75% { transform: translate(-30%, -70%) scale(1.1); opacity: 0.9; }
          }

          @keyframes shimmer {
            0% { background-position: -200% 0; }
            100% { background-position: 200% 0; }
          }

          @keyframes float {
            0%, 100% { transform: translateY(0px); }
            50% { transform: translateY(-10px); }
          }

          .card-shimmer {
            background: linear-gradient(90deg, transparent, rgba(255,255,255,0.1), transparent);
            background-size: 200% 100%;
            animation: shimmer 2s infinite;
          }
        `}
      </style>

      <section id="experience" style={{
        padding: '4rem 0',
        background: 'linear-gradient(135deg, #0a0a0a 0%, #1a1a1a 100%)',
        color: '#fff',
        minHeight: '100vh',
        position: 'relative',
        overflow: 'hidden'
      }}>
        {/* Enhanced Background decoration */}
        <div style={{
          position: 'absolute',
          top: 0,
          left: 0,
          width: '100%',
          height: '100%',
          background: `
            radial-gradient(circle at 80% 30%, rgba(0, 212, 255, 0.08) 0%, transparent 50%),
            radial-gradient(circle at 20% 70%, rgba(78, 205, 196, 0.08) 0%, transparent 50%),
            radial-gradient(circle at 50% 50%, rgba(255, 107, 107, 0.03) 0%, transparent 70%)
          `,
          pointerEvents: 'none'
        }}></div>

        {/* Floating Particles */}
        <div style={{
          position: 'absolute',
          top: 0,
          left: 0,
          width: '100%',
          height: '100%',
          pointerEvents: 'none'
        }}>
          {[...Array(6)].map((_, i) => (
            <div
              key={i}
              style={{
                position: 'absolute',
                width: '4px',
                height: '4px',
                background: i % 2 === 0 ? '#00d4ff' : '#4ecdc4',
                borderRadius: '50%',
                top: `${Math.random() * 100}%`,
                left: `${Math.random() * 100}%`,
                animation: `float ${3 + Math.random() * 2}s infinite ease-in-out`,
                animationDelay: `${Math.random() * 2}s`,
                opacity: 0.6
              }}
            />
          ))}
        </div>
      
      <div style={{
        maxWidth: '1200px',
        margin: '0 auto',
        padding: '0 2rem',
        position: 'relative',
        zIndex: 1
      }}>
        <h2 style={{
          textAlign: 'center',
          fontSize: '2.5rem',
          marginBottom: '3rem',
          color: '#00d4ff',
          fontWeight: '700'
        }}>
          Experience & Education
        </h2>

        {/* Tab Navigation with Controls */}
        <div style={{
          display: 'flex',
          justifyContent: 'center',
          alignItems: 'center',
          marginBottom: '3rem',
          gap: '1rem',
          flexWrap: 'wrap'
        }}>
          <button
            onClick={() => {
              setActiveTab('experience');
              setCurrentAutoIndex(0);
            }}
            style={{
              padding: '0.8rem 2rem',
              background: activeTab === 'experience' ? '#00d4ff' : 'transparent',
              color: activeTab === 'experience' ? '#000' : '#00d4ff',
              border: '2px solid #00d4ff',
              borderRadius: '25px',
              fontSize: '1rem',
              fontWeight: '600',
              cursor: 'pointer',
              transition: 'all 0.3s ease',
              display: 'flex',
              alignItems: 'center',
              gap: '0.5rem',
              transform: activeTab === 'experience' ? 'scale(1.05)' : 'scale(1)',
              boxShadow: activeTab === 'experience' ? '0 5px 15px rgba(0, 212, 255, 0.4)' : 'none'
            }}
          >
            <FaBriefcase /> Work Experience
          </button>

          <button
            onClick={() => {
              setActiveTab('education');
              setCurrentAutoIndex(0);
            }}
            style={{
              padding: '0.8rem 2rem',
              background: activeTab === 'education' ? '#4ecdc4' : 'transparent',
              color: activeTab === 'education' ? '#000' : '#4ecdc4',
              border: '2px solid #4ecdc4',
              borderRadius: '25px',
              fontSize: '1rem',
              fontWeight: '600',
              cursor: 'pointer',
              transition: 'all 0.3s ease',
              display: 'flex',
              alignItems: 'center',
              gap: '0.5rem',
              transform: activeTab === 'education' ? 'scale(1.05)' : 'scale(1)',
              boxShadow: activeTab === 'education' ? '0 5px 15px rgba(78, 205, 196, 0.4)' : 'none'
            }}
          >
            <FaGraduationCap /> Education
          </button>

          {/* Auto-play Control */}
          <button
            onClick={() => setIsAutoPlay(!isAutoPlay)}
            style={{
              padding: '0.8rem 1rem',
              background: isAutoPlay ? '#ff6b6b' : 'rgba(255, 255, 255, 0.1)',
              color: '#fff',
              border: `2px solid ${isAutoPlay ? '#ff6b6b' : 'rgba(255, 255, 255, 0.3)'}`,
              borderRadius: '25px',
              fontSize: '0.9rem',
              fontWeight: '600',
              cursor: 'pointer',
              transition: 'all 0.3s ease',
              display: 'flex',
              alignItems: 'center',
              gap: '0.5rem'
            }}
          >
            {isAutoPlay ? <FaPause /> : <FaPlay />}
            {isAutoPlay ? 'Pause' : 'Auto'}
          </button>
        </div>

        {/* Progress Indicator */}
        {isAutoPlay && (
          <div style={{
            display: 'flex',
            justifyContent: 'center',
            marginBottom: '2rem',
            gap: '0.5rem'
          }}>
            {(activeTab === 'experience' ? experienceData : educationData).map((_, index) => (
              <div
                key={index}
                style={{
                  width: '8px',
                  height: '8px',
                  borderRadius: '50%',
                  background: index === currentAutoIndex ?
                    (activeTab === 'experience' ? '#00d4ff' : '#4ecdc4') :
                    'rgba(255, 255, 255, 0.3)',
                  transition: 'all 0.3s ease',
                  cursor: 'pointer'
                }}
                onClick={() => setCurrentAutoIndex(index)}
              />
            ))}
          </div>
        )}

        {/* Cards Grid */}
        <div style={{
          display: 'grid',
          gridTemplateColumns: 'repeat(auto-fit, minmax(380px, 1fr))',
          gap: '2rem',
          marginBottom: '4rem'
        }}>
          {(activeTab === 'experience' ? experienceData : educationData).map((item, index) => {
            const isHovered = hoveredCard === item.id;
            const isAnimated = animatingCards.includes(index);
            const isAutoHighlighted = isAutoPlay && currentAutoIndex === index;

            return (
              <div
                key={item.id}
                onClick={() => setSelectedItem(item)}
                onMouseEnter={() => setHoveredCard(item.id)}
                onMouseLeave={() => setHoveredCard(null)}
                style={{
                  background: isAutoHighlighted ?
                    `linear-gradient(135deg, rgba(${activeTab === 'experience' ? '0, 212, 255' : '78, 205, 196'}, 0.2) 0%, rgba(26, 26, 26, 0.9) 100%)` :
                    'rgba(26, 26, 26, 0.8)',
                  padding: '2rem',
                  borderRadius: '20px',
                  border: isAutoHighlighted ?
                    `2px solid ${activeTab === 'experience' ? '#00d4ff' : '#4ecdc4'}` :
                    '1px solid rgba(255, 255, 255, 0.1)',
                  borderLeft: `6px solid ${activeTab === 'experience' ? '#00d4ff' : '#4ecdc4'}`,
                  backdropFilter: 'blur(15px)',
                  cursor: 'pointer',
                  transition: 'all 0.4s cubic-bezier(0.4, 0, 0.2, 1)',
                  position: 'relative',
                  overflow: 'hidden',
                  transform: isHovered ? 'translateY(-15px) scale(1.02)' :
                           isAutoHighlighted ? 'translateY(-8px) scale(1.01)' :
                           isAnimated ? 'translateY(0) scale(1)' : 'translateY(20px) scale(0.95)',
                  opacity: isAnimated ? 1 : 0,
                  boxShadow: isHovered ?
                    `0 25px 50px rgba(${activeTab === 'experience' ? '0, 212, 255' : '78, 205, 196'}, 0.4), 0 0 0 1px rgba(255, 255, 255, 0.1)` :
                    isAutoHighlighted ?
                    `0 15px 30px rgba(${activeTab === 'experience' ? '0, 212, 255' : '78, 205, 196'}, 0.3)` :
                    '0 5px 15px rgba(0, 0, 0, 0.3)',
                  animation: isAutoHighlighted ? 'pulse 2s infinite' : 'none'
                }}
              >
              {/* Floating Elements */}
              <div style={{
                position: 'absolute',
                top: '1rem',
                right: '1rem',
                display: 'flex',
                gap: '0.5rem'
              }}>
                {/* Rating Stars */}
                <div style={{ display: 'flex', gap: '2px' }}>
                  {[...Array(5)].map((_, starIndex) => (
                    <FaStar
                      key={starIndex}
                      size={12}
                      color={starIndex < 4 ? '#ffd700' : '#333'}
                      style={{
                        transition: 'all 0.2s ease',
                        transform: isHovered ? 'scale(1.2)' : 'scale(1)'
                      }}
                    />
                  ))}
                </div>
                {/* Achievement Badge */}
                <div style={{
                  background: 'rgba(255, 215, 0, 0.2)',
                  padding: '0.3rem',
                  borderRadius: '50%',
                  display: 'flex',
                  alignItems: 'center',
                  justifyContent: 'center'
                }}>
                  <FaAward color="#ffd700" size={12} />
                </div>
              </div>

              {/* Card Header */}
              <div style={{
                display: 'flex',
                justifyContent: 'space-between',
                alignItems: 'flex-start',
                marginBottom: '1.5rem',
                paddingTop: '1rem'
              }}>
                <div style={{ flex: 1 }}>
                  <div style={{
                    display: 'flex',
                    alignItems: 'center',
                    gap: '0.5rem',
                    marginBottom: '0.8rem'
                  }}>
                    <div style={{
                      background: `rgba(${activeTab === 'experience' ? '0, 212, 255' : '78, 205, 196'}, 0.2)`,
                      padding: '0.5rem',
                      borderRadius: '10px',
                      display: 'flex',
                      alignItems: 'center',
                      justifyContent: 'center',
                      transform: isHovered ? 'rotate(360deg)' : 'rotate(0deg)',
                      transition: 'transform 0.6s ease'
                    }}>
                      {activeTab === 'experience' ? <FaBriefcase color="#00d4ff" size={18} /> : <FaGraduationCap color="#4ecdc4" size={18} />}
                    </div>
                    <div>
                      <span style={{
                        fontSize: '0.8rem',
                        color: activeTab === 'experience' ? '#00d4ff' : '#4ecdc4',
                        fontWeight: '600',
                        textTransform: 'uppercase',
                        letterSpacing: '1px',
                        display: 'block'
                      }}>
                        {activeTab === 'experience' ? 'Work Experience' : 'Education'}
                      </span>
                      <span style={{
                        fontSize: '0.7rem',
                        color: '#888',
                        fontWeight: '400'
                      }}>
                        #{index + 1} of {(activeTab === 'experience' ? experienceData : educationData).length}
                      </span>
                    </div>
                  </div>
                  <h4 style={{
                    fontSize: '1.5rem',
                    marginBottom: '0.5rem',
                    color: '#fff',
                    fontWeight: '700',
                    lineHeight: '1.2',
                    transform: isHovered ? 'translateX(5px)' : 'translateX(0)',
                    transition: 'transform 0.3s ease'
                  }}>
                    {item.position || item.degree}
                  </h4>
                  <h5 style={{
                    fontSize: '1.2rem',
                    color: activeTab === 'experience' ? '#00d4ff' : '#4ecdc4',
                    marginBottom: '0.5rem',
                    fontWeight: '600',
                    transform: isHovered ? 'translateX(5px)' : 'translateX(0)',
                    transition: 'transform 0.3s ease 0.1s'
                  }}>
                    {item.company || item.institution}
                  </h5>
                </div>

                {/* Interactive View Button */}
                <div style={{
                  background: isHovered ?
                    `rgba(${activeTab === 'experience' ? '0, 212, 255' : '78, 205, 196'}, 0.2)` :
                    'rgba(255, 255, 255, 0.05)',
                  padding: '0.8rem',
                  borderRadius: '12px',
                  display: 'flex',
                  flexDirection: 'column',
                  alignItems: 'center',
                  gap: '0.3rem',
                  transition: 'all 0.3s ease',
                  transform: isHovered ? 'scale(1.1)' : 'scale(1)',
                  border: isHovered ?
                    `1px solid ${activeTab === 'experience' ? '#00d4ff' : '#4ecdc4'}` :
                    '1px solid rgba(255, 255, 255, 0.1)'
                }}>
                  <FaEye
                    color={isHovered ? (activeTab === 'experience' ? '#00d4ff' : '#4ecdc4') : '#888'}
                    size={16}
                  />
                  <span style={{
                    color: isHovered ? (activeTab === 'experience' ? '#00d4ff' : '#4ecdc4') : '#888',
                    fontSize: '0.7rem',
                    fontWeight: '600',
                    textTransform: 'uppercase',
                    letterSpacing: '0.5px'
                  }}>
                    View
                  </span>
                </div>
              </div>

              {/* Card Content */}
              <div style={{ marginBottom: '1.5rem' }}>
                <div style={{
                  display: 'flex',
                  alignItems: 'center',
                  gap: '1rem',
                  marginBottom: '1rem',
                  flexWrap: 'wrap'
                }}>
                  <div style={{
                    display: 'flex',
                    alignItems: 'center',
                    gap: '0.3rem',
                    color: '#888',
                    fontSize: '0.9rem'
                  }}>
                    <FaCalendarAlt />
                    <span>{item.duration}</span>
                  </div>
                  {item.location && (
                    <div style={{
                      display: 'flex',
                      alignItems: 'center',
                      gap: '0.3rem',
                      color: '#888',
                      fontSize: '0.9rem'
                    }}>
                      <FaMapMarkerAlt />
                      <span>{item.location}</span>
                    </div>
                  )}
                  {item.type && (
                    <span style={{
                      background: 'rgba(0, 212, 255, 0.2)',
                      color: '#00d4ff',
                      padding: '0.2rem 0.8rem',
                      borderRadius: '12px',
                      fontSize: '0.8rem',
                      fontWeight: '600'
                    }}>
                      {item.type}
                    </span>
                  )}
                </div>
                <p style={{
                  color: '#ccc',
                  lineHeight: '1.6',
                  fontSize: '0.95rem'
                }}>
                  {item.description}
                </p>
              </div>

              {/* Interactive Stats */}
              <div style={{
                display: 'flex',
                gap: '1rem',
                marginBottom: '1.5rem',
                padding: '1rem',
                background: 'rgba(255, 255, 255, 0.03)',
                borderRadius: '12px',
                border: '1px solid rgba(255, 255, 255, 0.05)'
              }}>
                <div style={{
                  display: 'flex',
                  alignItems: 'center',
                  gap: '0.5rem',
                  flex: 1
                }}>
                  <div style={{
                    background: 'rgba(0, 212, 255, 0.2)',
                    padding: '0.5rem',
                    borderRadius: '8px'
                  }}>
                    <FaCode color="#00d4ff" size={14} />
                  </div>
                  <div>
                    <div style={{ color: '#fff', fontSize: '1.1rem', fontWeight: '700' }}>
                      {item.technologies ? item.technologies.length : '5+'}
                    </div>
                    <div style={{ color: '#888', fontSize: '0.7rem' }}>Technologies</div>
                  </div>
                </div>

                <div style={{
                  display: 'flex',
                  alignItems: 'center',
                  gap: '0.5rem',
                  flex: 1
                }}>
                  <div style={{
                    background: 'rgba(78, 205, 196, 0.2)',
                    padding: '0.5rem',
                    borderRadius: '8px'
                  }}>
                    <FaUsers color="#4ecdc4" size={14} />
                  </div>
                  <div>
                    <div style={{ color: '#fff', fontSize: '1.1rem', fontWeight: '700' }}>
                      {activeTab === 'experience' ? '5+' : '200+'}
                    </div>
                    <div style={{ color: '#888', fontSize: '0.7rem' }}>
                      {activeTab === 'experience' ? 'Team Size' : 'Students'}
                    </div>
                  </div>
                </div>
              </div>

              {/* Technologies Preview with Animation */}
              {item.technologies && (
                <div style={{ marginBottom: '1.5rem' }}>
                  <div style={{
                    display: 'flex',
                    alignItems: 'center',
                    gap: '0.5rem',
                    marginBottom: '0.8rem'
                  }}>
                    <FaCode color={activeTab === 'experience' ? '#00d4ff' : '#4ecdc4'} size={14} />
                    <span style={{
                      color: '#fff',
                      fontSize: '0.9rem',
                      fontWeight: '600'
                    }}>
                      Tech Stack
                    </span>
                  </div>
                  <div style={{
                    display: 'flex',
                    flexWrap: 'wrap',
                    gap: '0.6rem'
                  }}>
                    {item.technologies.slice(0, 4).map((tech, techIndex) => (
                      <span
                        key={techIndex}
                        style={{
                          background: isHovered ?
                            `rgba(${activeTab === 'experience' ? '0, 212, 255' : '78, 205, 196'}, 0.2)` :
                            'rgba(255, 255, 255, 0.1)',
                          color: isHovered ?
                            (activeTab === 'experience' ? '#00d4ff' : '#4ecdc4') : '#fff',
                          padding: '0.4rem 0.9rem',
                          borderRadius: '18px',
                          fontSize: '0.8rem',
                          fontWeight: '500',
                          transition: 'all 0.3s ease',
                          transform: isHovered ? 'translateY(-2px)' : 'translateY(0)',
                          border: isHovered ?
                            `1px solid ${activeTab === 'experience' ? '#00d4ff' : '#4ecdc4'}` :
                            '1px solid transparent',
                          cursor: 'pointer'
                        }}
                        onMouseEnter={(e) => {
                          e.target.style.transform = 'translateY(-4px) scale(1.05)';
                        }}
                        onMouseLeave={(e) => {
                          e.target.style.transform = isHovered ? 'translateY(-2px) scale(1)' : 'translateY(0) scale(1)';
                        }}
                      >
                        {tech}
                      </span>
                    ))}
                    {item.technologies.length > 4 && (
                      <span style={{
                        color: '#888',
                        fontSize: '0.8rem',
                        padding: '0.4rem 0.6rem',
                        background: 'rgba(255, 255, 255, 0.05)',
                        borderRadius: '18px',
                        border: '1px dashed rgba(255, 255, 255, 0.2)'
                      }}>
                        +{item.technologies.length - 4} more
                      </span>
                    )}
                  </div>
                </div>
              )}

              {/* Interactive Footer */}
              <div style={{
                display: 'flex',
                justifyContent: 'space-between',
                alignItems: 'center',
                paddingTop: '1.5rem',
                borderTop: `1px solid rgba(${activeTab === 'experience' ? '0, 212, 255' : '78, 205, 196'}, 0.2)`,
                marginTop: '1rem'
              }}>
                <div style={{
                  display: 'flex',
                  alignItems: 'center',
                  gap: '1rem'
                }}>
                  <span style={{
                    color: '#888',
                    fontSize: '0.9rem',
                    fontWeight: '500'
                  }}>
                    Click for full details
                  </span>

                  {/* Achievement Count */}
                  <div style={{
                    display: 'flex',
                    alignItems: 'center',
                    gap: '0.3rem',
                    background: 'rgba(255, 215, 0, 0.1)',
                    padding: '0.3rem 0.6rem',
                    borderRadius: '12px',
                    border: '1px solid rgba(255, 215, 0, 0.3)'
                  }}>
                    <FaAward color="#ffd700" size={12} />
                    <span style={{
                      color: '#ffd700',
                      fontSize: '0.8rem',
                      fontWeight: '600'
                    }}>
                      {item.achievements ? item.achievements.length : 3} achievements
                    </span>
                  </div>
                </div>

                {/* Animated Arrow */}
                <div style={{
                  display: 'flex',
                  alignItems: 'center',
                  gap: '0.5rem',
                  color: activeTab === 'experience' ? '#00d4ff' : '#4ecdc4',
                  transform: isHovered ? 'translateX(5px)' : 'translateX(0)',
                  transition: 'transform 0.3s ease'
                }}>
                  <span style={{
                    fontSize: '0.9rem',
                    fontWeight: '600',
                    opacity: isHovered ? 1 : 0.7,
                    transition: 'opacity 0.3s ease'
                  }}>
                    Explore
                  </span>
                  <FaChevronRight
                    size={14}
                    style={{
                      transform: isHovered ? 'translateX(3px)' : 'translateX(0)',
                      transition: 'transform 0.3s ease'
                    }}
                  />
                </div>
              </div>

              {/* Dynamic Background Effects */}
              <div style={{
                position: 'absolute',
                top: 0,
                left: 0,
                right: 0,
                bottom: 0,
                background: isHovered ?
                  `radial-gradient(circle at ${Math.random() * 100}% ${Math.random() * 100}%, rgba(${activeTab === 'experience' ? '0, 212, 255' : '78, 205, 196'}, 0.1) 0%, transparent 70%)` :
                  'transparent',
                opacity: isHovered ? 1 : 0,
                transition: 'opacity 0.5s ease',
                pointerEvents: 'none',
                borderRadius: '20px'
              }}></div>

              {/* Particle Effect */}
              {isHovered && (
                <div style={{
                  position: 'absolute',
                  top: '50%',
                  left: '50%',
                  width: '4px',
                  height: '4px',
                  background: activeTab === 'experience' ? '#00d4ff' : '#4ecdc4',
                  borderRadius: '50%',
                  transform: 'translate(-50%, -50%)',
                  animation: 'particle-float 2s infinite ease-in-out',
                  pointerEvents: 'none'
                }}></div>
              )}

              {/* Progress Bar */}
              <div style={{
                position: 'absolute',
                bottom: 0,
                left: 0,
                height: '3px',
                background: `linear-gradient(90deg, ${activeTab === 'experience' ? '#00d4ff' : '#4ecdc4'} 0%, transparent 100%)`,
                width: isHovered ? '100%' : '0%',
                transition: 'width 0.5s ease',
                borderRadius: '0 0 20px 20px'
              }}></div>
            </div>
            );
          })}
        </div>

        {/* Modal for detailed view */}
        {selectedItem && (
          <div style={{
            position: 'fixed',
            top: 0,
            left: 0,
            width: '100%',
            height: '100%',
            background: 'rgba(0, 0, 0, 0.8)',
            display: 'flex',
            justifyContent: 'center',
            alignItems: 'center',
            zIndex: 1000,
            padding: '2rem'
          }} onClick={() => setSelectedItem(null)}>
            <div style={{
              background: 'linear-gradient(135deg, #1a1a1a 0%, #2a2a2a 100%)',
              borderRadius: '20px',
              padding: '2rem',
              maxWidth: '800px',
              width: '100%',
              maxHeight: '90vh',
              overflowY: 'auto',
              position: 'relative',
              border: `2px solid ${activeTab === 'experience' ? '#00d4ff' : '#4ecdc4'}`
            }} onClick={(e) => e.stopPropagation()}>
              {/* Close button */}
              <button
                onClick={() => setSelectedItem(null)}
                style={{
                  position: 'absolute',
                  top: '1rem',
                  right: '1rem',
                  background: 'rgba(255, 255, 255, 0.1)',
                  border: 'none',
                  borderRadius: '50%',
                  width: '40px',
                  height: '40px',
                  display: 'flex',
                  alignItems: 'center',
                  justifyContent: 'center',
                  cursor: 'pointer',
                  color: '#fff',
                  fontSize: '1.2rem'
                }}
              >
                <FaTimes />
              </button>

              {/* Modal Header */}
              <div style={{ marginBottom: '2rem' }}>
                <div style={{
                  display: 'flex',
                  alignItems: 'center',
                  gap: '0.5rem',
                  marginBottom: '1rem'
                }}>
                  {activeTab === 'experience' ? <FaBriefcase color="#00d4ff" size={24} /> : <FaGraduationCap color="#4ecdc4" size={24} />}
                  <span style={{
                    fontSize: '1rem',
                    color: activeTab === 'experience' ? '#00d4ff' : '#4ecdc4',
                    fontWeight: '600',
                    textTransform: 'uppercase',
                    letterSpacing: '1px'
                  }}>
                    {activeTab === 'experience' ? 'Work Experience' : 'Education'}
                  </span>
                </div>
                <h3 style={{
                  fontSize: '2rem',
                  marginBottom: '0.5rem',
                  color: '#fff',
                  fontWeight: '700'
                }}>
                  {selectedItem.position || selectedItem.degree}
                </h3>
                <h4 style={{
                  fontSize: '1.3rem',
                  color: activeTab === 'experience' ? '#00d4ff' : '#4ecdc4',
                  marginBottom: '1rem',
                  fontWeight: '600'
                }}>
                  {selectedItem.company || selectedItem.institution}
                </h4>
                <div style={{
                  display: 'flex',
                  alignItems: 'center',
                  gap: '2rem',
                  flexWrap: 'wrap'
                }}>
                  <div style={{
                    display: 'flex',
                    alignItems: 'center',
                    gap: '0.5rem',
                    color: '#888'
                  }}>
                    <FaCalendarAlt />
                    <span>{selectedItem.duration}</span>
                  </div>
                  {selectedItem.location && (
                    <div style={{
                      display: 'flex',
                      alignItems: 'center',
                      gap: '0.5rem',
                      color: '#888'
                    }}>
                      <FaMapMarkerAlt />
                      <span>{selectedItem.location}</span>
                    </div>
                  )}
                  {selectedItem.type && (
                    <span style={{
                      background: `rgba(${activeTab === 'experience' ? '0, 212, 255' : '78, 205, 196'}, 0.2)`,
                      color: activeTab === 'experience' ? '#00d4ff' : '#4ecdc4',
                      padding: '0.3rem 1rem',
                      borderRadius: '15px',
                      fontSize: '0.9rem',
                      fontWeight: '600'
                    }}>
                      {selectedItem.type}
                    </span>
                  )}
                </div>
              </div>

              {/* Modal Content */}
              <div style={{ color: '#ccc', lineHeight: '1.6' }}>
                <p style={{ marginBottom: '2rem', fontSize: '1.1rem' }}>
                  {selectedItem.description}
                </p>

                {/* Responsibilities */}
                {selectedItem.responsibilities && (
                  <div style={{ marginBottom: '2rem' }}>
                    <h5 style={{
                      color: '#fff',
                      fontSize: '1.2rem',
                      marginBottom: '1rem',
                      fontWeight: '600'
                    }}>
                      Key Responsibilities
                    </h5>
                    <ul style={{ paddingLeft: '1.5rem' }}>
                      {selectedItem.responsibilities.map((resp, index) => (
                        <li key={index} style={{ marginBottom: '0.5rem' }}>
                          {resp}
                        </li>
                      ))}
                    </ul>
                  </div>
                )}

                {/* Technologies */}
                {selectedItem.technologies && (
                  <div style={{ marginBottom: '2rem' }}>
                    <h5 style={{
                      color: '#fff',
                      fontSize: '1.2rem',
                      marginBottom: '1rem',
                      fontWeight: '600'
                    }}>
                      Technologies Used
                    </h5>
                    <div style={{
                      display: 'flex',
                      flexWrap: 'wrap',
                      gap: '0.8rem'
                    }}>
                      {selectedItem.technologies.map((tech, index) => (
                        <span
                          key={index}
                          style={{
                            background: 'rgba(255, 255, 255, 0.1)',
                            color: '#fff',
                            padding: '0.5rem 1rem',
                            borderRadius: '20px',
                            fontSize: '0.9rem',
                            fontWeight: '500',
                            border: `1px solid ${activeTab === 'experience' ? '#00d4ff' : '#4ecdc4'}`
                          }}
                        >
                          {tech}
                        </span>
                      ))}
                    </div>
                  </div>
                )}

                {/* Achievements */}
                {selectedItem.achievements && (
                  <div style={{ marginBottom: '2rem' }}>
                    <h5 style={{
                      color: '#fff',
                      fontSize: '1.2rem',
                      marginBottom: '1rem',
                      fontWeight: '600'
                    }}>
                      Key Achievements
                    </h5>
                    <ul style={{ paddingLeft: '1.5rem' }}>
                      {selectedItem.achievements.map((achievement, index) => (
                        <li key={index} style={{ marginBottom: '0.5rem' }}>
                          {achievement}
                        </li>
                      ))}
                    </ul>
                  </div>
                )}

                {/* Courses (for education) */}
                {selectedItem.courses && (
                  <div style={{ marginBottom: '2rem' }}>
                    <h5 style={{
                      color: '#fff',
                      fontSize: '1.2rem',
                      marginBottom: '1rem',
                      fontWeight: '600'
                    }}>
                      Relevant Courses
                    </h5>
                    <div style={{
                      display: 'flex',
                      flexWrap: 'wrap',
                      gap: '0.8rem'
                    }}>
                      {selectedItem.courses.map((course, index) => (
                        <span
                          key={index}
                          style={{
                            background: 'rgba(78, 205, 196, 0.1)',
                            color: '#4ecdc4',
                            padding: '0.5rem 1rem',
                            borderRadius: '20px',
                            fontSize: '0.9rem',
                            fontWeight: '500',
                            border: '1px solid #4ecdc4'
                          }}
                        >
                          {course}
                        </span>
                      ))}
                    </div>
                  </div>
                )}

                {/* Final Project (for education) */}
                {selectedItem.finalProject && (
                  <div>
                    <h5 style={{
                      color: '#fff',
                      fontSize: '1.2rem',
                      marginBottom: '1rem',
                      fontWeight: '600'
                    }}>
                      Final Project
                    </h5>
                    <p style={{
                      background: 'rgba(78, 205, 196, 0.1)',
                      padding: '1rem',
                      borderRadius: '10px',
                      border: '1px solid #4ecdc4'
                    }}>
                      {selectedItem.finalProject}
                    </p>
                  </div>
                )}
              </div>
            </div>
          </div>
        )}
      </div>
    </section>
    </>
  );
};

export default ExperienceCards;
