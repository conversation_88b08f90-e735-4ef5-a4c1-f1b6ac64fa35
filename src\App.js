import React, { useState } from 'react';
import './App.css';
import { ThemeProvider } from './contexts/ThemeContext';
import LoadingScreen from './components/LoadingScreen';
import Navbar from './components/Navbar';
import Hero from './components/Hero';
import About from './components/About';
import Skills from './components/Skills';
import Projects from './components/Projects';
import Experience from './components/ExperienceCards';
import Contact from './components/Contact';
import InteractiveCursor from './components/InteractiveCursor';
import FloatingActionButton from './components/FloatingActionButton';
import InteractiveBackground from './components/InteractiveBackground';
import ScrollProgress from './components/ScrollProgress';
import EasterEgg from './components/EasterEgg';
import Footer from './components/Footer';

function App() {
  const [isLoading, setIsLoading] = useState(true);

  const handleLoadingComplete = () => {
    setIsLoading(false);
  };

  return (
    <ThemeProvider>
      <div className="App">
        {/* Loading Screen */}
        {isLoading && (
          <LoadingScreen onLoadingComplete={handleLoadingComplete} />
        )}

        {/* Main App Content */}
        {!isLoading && (
          <>
            {/* Interactive Background */}
            <InteractiveBackground />

            {/* Interactive Cursor */}
            <InteractiveCursor />

            {/* Scroll Progress Indicator */}
            <ScrollProgress />

            {/* Main Content */}
            <Navbar />
            <Hero />
            <About />
            <Skills />
            <Projects />
            <Experience />
            <Contact />
            <Footer />

            {/* Floating Action Button */}
            <FloatingActionButton />

            {/* Easter Egg */}
            <EasterEgg />
          </>
        )}
      </div>
    </ThemeProvider>
  );
}

export default App;
