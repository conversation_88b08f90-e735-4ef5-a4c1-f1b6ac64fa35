import React, { useState } from 'react';
import { FaRocket, FaDownload, FaEnvelope, FaGithub, FaLinkedin, FaWhatsapp } from 'react-icons/fa';

const FloatingActionButton = () => {
  const [isExpanded, setIsExpanded] = useState(false);

  const menuItems = [
    {
      icon: FaDownload,
      label: 'Download CV',
      color: '#4ecdc4',
      action: () => {
        // Create a dummy CV download
        const link = document.createElement('a');
        link.href = 'data:text/plain;charset=utf-8,CV - Portfolio%0A%0AThis is a sample CV file.';
        link.download = 'CV-Portfolio.txt';
        link.click();
      }
    },
    {
      icon: FaEnvelope,
      label: 'Email Me',
      color: '#ff6b6b',
      action: () => window.open('mailto:<EMAIL>', '_blank')
    },
    {
      icon: FaWhatsapp,
      label: 'WhatsApp',
      color: '#25d366',
      action: () => window.open('https://wa.me/6283123757272', '_blank')
    },
    {
      icon: FaGithub,
      label: 'GitHub',
      color: '#333',
      action: () => window.open('https://github.com', '_blank')
    },
    {
      icon: FaLinkedin,
      label: 'LinkedIn',
      color: '#0077b5',
      action: () => window.open('https://linkedin.com', '_blank')
    }
  ];

  return (
    <div style={{
      position: 'fixed',
      bottom: '2rem',
      right: '2rem',
      zIndex: 1000
    }}>
      {/* Menu Items */}
      {isExpanded && menuItems.map((item, index) => {
        const IconComponent = item.icon;
        const angle = (index * 45) - 90; // Spread items in a fan
        const radius = 80;
        const x = Math.cos(angle * Math.PI / 180) * radius;
        const y = Math.sin(angle * Math.PI / 180) * radius;
        
        return (
          <div
            key={index}
            onClick={item.action}
            style={{
              position: 'absolute',
              bottom: 70 - y,
              right: 70 - x,
              width: '50px',
              height: '50px',
              backgroundColor: item.color,
              borderRadius: '50%',
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',
              cursor: 'pointer',
              transition: 'all 0.3s cubic-bezier(0.4, 0, 0.2, 1)',
              transform: isExpanded ? 'scale(1) rotate(0deg)' : 'scale(0) rotate(180deg)',
              opacity: isExpanded ? 1 : 0,
              boxShadow: '0 4px 20px rgba(0, 0, 0, 0.3)',
              animation: isExpanded ? `fadeInUp 0.3s ease ${index * 0.1}s both` : 'none'
            }}
            onMouseEnter={(e) => {
              e.target.style.transform = 'scale(1.1)';
              e.target.style.boxShadow = `0 6px 25px ${item.color}40`;
            }}
            onMouseLeave={(e) => {
              e.target.style.transform = 'scale(1)';
              e.target.style.boxShadow = '0 4px 20px rgba(0, 0, 0, 0.3)';
            }}
          >
            <IconComponent color="white" size={20} />
            
            {/* Tooltip */}
            <div style={{
              position: 'absolute',
              right: '60px',
              top: '50%',
              transform: 'translateY(-50%)',
              backgroundColor: 'rgba(0, 0, 0, 0.8)',
              color: 'white',
              padding: '0.5rem 1rem',
              borderRadius: '20px',
              fontSize: '0.8rem',
              whiteSpace: 'nowrap',
              opacity: 0,
              transition: 'opacity 0.3s ease',
              pointerEvents: 'none'
            }}
            className="tooltip">
              {item.label}
            </div>
          </div>
        );
      })}

      {/* Main FAB */}
      <div
        onClick={() => setIsExpanded(!isExpanded)}
        style={{
          width: '70px',
          height: '70px',
          backgroundColor: '#00d4ff',
          borderRadius: '50%',
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center',
          cursor: 'pointer',
          transition: 'all 0.3s ease',
          boxShadow: '0 8px 30px rgba(0, 212, 255, 0.4)',
          transform: isExpanded ? 'rotate(45deg) scale(1.1)' : 'rotate(0deg) scale(1)',
          position: 'relative',
          overflow: 'hidden'
        }}
        onMouseEnter={(e) => {
          if (!isExpanded) {
            e.target.style.transform = 'scale(1.1)';
            e.target.style.boxShadow = '0 10px 35px rgba(0, 212, 255, 0.6)';
          }
        }}
        onMouseLeave={(e) => {
          if (!isExpanded) {
            e.target.style.transform = 'scale(1)';
            e.target.style.boxShadow = '0 8px 30px rgba(0, 212, 255, 0.4)';
          }
        }}
      >
        {/* Ripple Effect */}
        <div style={{
          position: 'absolute',
          top: 0,
          left: 0,
          width: '100%',
          height: '100%',
          background: 'radial-gradient(circle, rgba(255,255,255,0.3) 0%, transparent 70%)',
          borderRadius: '50%',
          transform: isExpanded ? 'scale(2)' : 'scale(0)',
          transition: 'transform 0.3s ease',
          pointerEvents: 'none'
        }} />
        
        <FaRocket 
          color="white" 
          size={24}
          style={{
            transform: isExpanded ? 'rotate(-45deg)' : 'rotate(0deg)',
            transition: 'transform 0.3s ease'
          }}
        />
      </div>

      {/* CSS Animations */}
      <style>
        {`
          @keyframes fadeInUp {
            from {
              opacity: 0;
              transform: translateY(20px) scale(0.8);
            }
            to {
              opacity: 1;
              transform: translateY(0) scale(1);
            }
          }
          
          .tooltip {
            opacity: 0 !important;
          }
          
          div:hover .tooltip {
            opacity: 1 !important;
          }
        `}
      </style>
    </div>
  );
};

export default FloatingActionButton;
