import React, { useState, useEffect } from 'react';
import { FaExternalLinkAlt, FaGithub, FaStar, FaSearch, FaFilter } from 'react-icons/fa';
import { projects } from '../data/portfolioData';
import './Projects.css';

const Projects = () => {
  const [filter, setFilter] = useState('all');
  const [searchTerm, setSearchTerm] = useState('');
  const [hoveredProject, setHoveredProject] = useState(null);
  const [visibleProjects, setVisibleProjects] = useState([]);

  // Get all unique technologies for filter
  const allTechnologies = [...new Set(projects.flatMap(project => project.technologies))];

  const filteredProjects = projects.filter(project => {
    const matchesFilter = filter === 'all' ||
                         (filter === 'featured' && project.featured) ||
                         (filter !== 'all' && filter !== 'featured' && project.technologies.includes(filter));

    const matchesSearch = project.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         project.description.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         project.technologies.some(tech => tech.toLowerCase().includes(searchTerm.toLowerCase()));

    return matchesFilter && matchesSearch;
  });

  const filterButtons = [
    { key: 'all', label: 'All Projects', icon: FaFilter },
    { key: 'featured', label: 'Featured', icon: FaStar },
    ...allTechnologies.slice(0, 4).map(tech => ({ key: tech, label: tech, icon: FaFilter }))
  ];

  // Animate projects on load
  useEffect(() => {
    setVisibleProjects([]);
    const timer = setTimeout(() => {
      filteredProjects.forEach((_, index) => {
        setTimeout(() => {
          setVisibleProjects(prev => [...prev, index]);
        }, index * 100);
      });
    }, 100);

    return () => clearTimeout(timer);
  }, [filteredProjects]);

  return (
    <section id="projects" className="section projects">
      <div className="container">
        <h2 className="section-title">My Projects</h2>

        {/* Search Bar */}
        <div className="projects-search">
          <div className="search-wrapper">
            <FaSearch className="search-icon" />
            <input
              type="text"
              placeholder="Search projects, technologies..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="search-input"
            />
          </div>
        </div>

        {/* Interactive Filter Buttons */}
        <div className="projects-filter">
          {filterButtons.map((button) => {
            const IconComponent = button.icon;
            return (
              <button
                key={button.key}
                className={`filter-btn ${filter === button.key ? 'active' : ''}`}
                onClick={() => setFilter(button.key)}
              >
                <IconComponent className="filter-icon" />
                <span>{button.label}</span>
              </button>
            );
          })}
        </div>

        {/* Projects Counter */}
        <div className="projects-counter">
          <span>{filteredProjects.length} project{filteredProjects.length !== 1 ? 's' : ''} found</span>
        </div>
        
        <div className="projects-grid">
          {filteredProjects.map((project, index) => (
            <div
              key={project.id}
              className={`project-card ${visibleProjects.includes(index) ? 'visible' : ''} ${hoveredProject === project.id ? 'hovered' : ''}`}
              onMouseEnter={() => setHoveredProject(project.id)}
              onMouseLeave={() => setHoveredProject(null)}
              style={{ animationDelay: `${index * 0.1}s` }}
            >
              {project.featured && (
                <div className="featured-badge">
                  <FaStar /> Featured
                </div>
              )}

              <div className="project-image">
                <div className="image-placeholder">
                  <span>Project Screenshot</span>
                </div>
                <div className={`project-overlay ${hoveredProject === project.id ? 'active' : ''}`}>
                  <div className="project-links">
                    <a
                      href={project.liveLink}
                      target="_blank"
                      rel="noopener noreferrer"
                      className="project-link live-link"
                      title="View Live"
                    >
                      <FaExternalLinkAlt />
                      <span>Live Demo</span>
                    </a>
                    <a
                      href={project.githubLink}
                      target="_blank"
                      rel="noopener noreferrer"
                      className="project-link github-link"
                      title="View Code"
                    >
                      <FaGithub />
                      <span>Source Code</span>
                    </a>
                  </div>
                </div>
              </div>
              
              <div className="project-content">
                <h3 className="project-title">{project.title}</h3>
                <p className="project-description">{project.description}</p>
                
                <div className="project-technologies">
                  {project.technologies.map((tech, techIndex) => (
                    <span key={techIndex} className="tech-tag">
                      {tech}
                    </span>
                  ))}
                </div>
                
                <div className="project-actions">
                  <a 
                    href={project.liveLink} 
                    target="_blank" 
                    rel="noopener noreferrer"
                    className="btn-primary"
                  >
                    <FaExternalLinkAlt /> Live Demo
                  </a>
                  <a 
                    href={project.githubLink} 
                    target="_blank" 
                    rel="noopener noreferrer"
                    className="btn-secondary"
                  >
                    <FaGithub /> Source Code
                  </a>
                </div>
              </div>
            </div>
          ))}
        </div>
        
        <div className="projects-cta">
          <div className="cta-content animate-fadeInUp">
            <h3>Interested in working together?</h3>
            <p>I'm always open to discussing new opportunities and exciting projects.</p>
            <a href="#contact" className="btn-primary">
              Let's Talk
            </a>
          </div>
        </div>
      </div>
    </section>
  );
};

export default Projects;
